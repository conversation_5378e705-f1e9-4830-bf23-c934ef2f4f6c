using System.Collections.Concurrent;
using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class VideoService : IVideoService
    {
        private readonly ConcurrentDictionary<string, VideoRoom> _rooms = new();
        private readonly ConcurrentDictionary<string, string> _userNames = new();

        public async Task RegisterUserAsync(string connectionId, string name)
        {
            _userNames[connectionId] = name;
            await Task.CompletedTask;
        }

        public async Task AddToRoomAsync(string connectionId, string roomId)
        {
            if (!_rooms.ContainsKey(roomId))
            {
                _rooms[roomId] = new VideoRoom
                {
                    RoomId = roomId,
                    Participants = new HashSet<string>()
                };
            }

            _rooms[roomId].Participants.Add(connectionId);
            await Task.CompletedTask;
        }

        public async Task RemoveFromRoomAsync(string connectionId, string roomId)
        {
            if (_rooms.TryGetValue(roomId, out var room))
            {
                room.Participants.Remove(connectionId);

                // Remove room if empty
                if (room.Participants.Count == 0)
                {
                    _rooms.TryRemove(roomId, out _);
                    Console.WriteLine($"Video server: Room {roomId} deleted (empty)");
                }
            }

            await Task.CompletedTask;
        }

        public async Task RemoveFromAllRoomsAsync(string connectionId)
        {
            var roomsToUpdate = new List<string>();

            foreach (var kvp in _rooms)
            {
                if (kvp.Value.Participants.Contains(connectionId))
                {
                    roomsToUpdate.Add(kvp.Key);
                }
            }

            foreach (var roomId in roomsToUpdate)
            {
                await RemoveFromRoomAsync(connectionId, roomId);
            }

            // Remove user name
            _userNames.TryRemove(connectionId, out _);
        }

        public async Task<List<VideoUser>> GetOtherParticipantsAsync(string roomId, string excludeConnectionId)
        {
            var participants = new List<VideoUser>();

            if (_rooms.TryGetValue(roomId, out var room))
            {
                foreach (var participantId in room.Participants)
                {
                    if (participantId != excludeConnectionId)
                    {
                        var name = _userNames.TryGetValue(participantId, out var userName) ? userName : "Unknown Player";
                        participants.Add(new VideoUser
                        {
                            Id = participantId,
                            Name = name
                        });
                    }
                }
            }

            return await Task.FromResult(participants);
        }

        public async Task<(bool IsValid, string? Error)> ValidateSignalAsync(string fromConnectionId, string toConnectionId, string roomId)
        {
            // Check if the room exists
            if (!_rooms.TryGetValue(roomId, out var room))
            {
                return (false, "Room not found");
            }

            // Check if both users are in the room
            if (!room.Participants.Contains(fromConnectionId))
            {
                return (false, "Sender not in room");
            }

            if (!room.Participants.Contains(toConnectionId))
            {
                return (false, "Recipient not in room");
            }

            return await Task.FromResult((true, null));
        }

        public async Task<string?> GetUserNameAsync(string connectionId)
        {
            _userNames.TryGetValue(connectionId, out var name);
            return await Task.FromResult(name);
        }
    }
}
