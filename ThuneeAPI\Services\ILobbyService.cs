using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface ILobbyService
    {
        // Lobby management
        Task<CreateLobbyResponse> CreateLobbyAsync(string playerName, string? teamName = null, TimeSettings? timeSettings = null);
        Task<JoinLobbyResponse> JoinLobbyAsync(string lobbyCode, string playerName);
        Task<bool> UpdateTeamNameAsync(string connectionId, string? lobbyCode, int teamNumber, string teamName);
        Task<bool> SetTeamReadyAsync(string connectionId, string? lobbyCode, bool ready);
        Task<bool> SwitchTeamAsync(string connectionId, string? lobbyCode);
        Task<bool> StartGameAsync(string connectionId, string? lobbyCode);
        Task<bool> FindMatchAsync(string connectionId, string? lobbyCode);
        Task<bool> CancelFindMatchAsync(string connectionId, string? lobbyCode);

        // Lobby state
        Lobby? GetLobby(string lobbyCode);
        Lobby? GetLobbyByConnectionId(string connectionId);
        string? GetLobbyCodeByConnectionId(string connectionId);
        void SetLobbyForConnection(string connectionId, string lobbyCode);
        void RemoveConnectionFromLobby(string connectionId);
        
        // Match making
        void AddToMatchQueue(string lobbyCode);
        void RemoveFromMatchQueue(string lobbyCode);
        Task CheckForMatchesAsync();
        
        // Utility
        string GenerateLobbyCode();
        string GetAvatarUrl(string name);
        
        // Game lobby management
        GameLobby? CreateGameLobby(string lobby1Code, string lobby2Code);
        GameLobby? GetGameLobby(string gameLobbyCode);
        void SetGameLobbyForConnection(string connectionId, string gameLobbyCode);
        string? GetGameLobbyCodeByConnectionId(string connectionId);
        
        // Events
        event Func<string, PlayersUpdatedResponse, Task>? PlayersUpdated;
        event Func<string, TeamNamesUpdatedResponse, Task>? TeamNamesUpdated;
        event Func<string, TeamReadyUpdatedResponse, Task>? TeamReadyUpdated;
        event Func<string, GameStartedResponse, Task>? GameStarted;
        event Func<string, GamePhaseUpdatedResponse, Task>? GamePhaseUpdated;
        event Func<string, MatchFoundResponse, Task>? MatchFound;
        event Func<string, MatchStatusUpdateResponse, Task>? MatchStatusUpdated;
    }
}
