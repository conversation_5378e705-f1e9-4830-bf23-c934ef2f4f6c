// SignalR Service for React Frontend
// Replace your existing socketService.ts with this implementation

import * as signalR from '@microsoft/signalr';

interface ThuneeConfig {
  isDevelopment: boolean;
  getApiBaseUrl(): string;
  getGameHubUrl(): string;
  getVideoHubUrl(): string;
  getSocketConfig(): any;
}

// Configuration helper
const getConfig = (): ThuneeConfig => {
  const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  
  return {
    isDevelopment,
    getApiBaseUrl() {
      if (isDevelopment) {
        return 'http://localhost:5000';
      } else {
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        return `${protocol}//${hostname}:3001`;
      }
    },
    getGameHubUrl() {
      return `${this.getApiBaseUrl()}/gameHub`;
    },
    getVideoHubUrl() {
      return `${this.getApiBaseUrl()}/videoHub`;
    },
    getSocketConfig() {
      return {
        skipNegotiation: false,
        transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling,
        accessTokenFactory: () => '', // Add token if needed
        withCredentials: true
      };
    }
  };
};

class SignalRService {
  private gameConnection: signalR.HubConnection | null = null;
  private videoConnection: signalR.HubConnection | null = null;
  private config = getConfig();

  // Game Hub Connection
  async connectToGameHub(): Promise<signalR.HubConnection> {
    if (this.gameConnection?.state === signalR.HubConnectionState.Connected) {
      return this.gameConnection;
    }

    this.gameConnection = new signalR.HubConnectionBuilder()
      .withUrl(this.config.getGameHubUrl(), this.config.getSocketConfig())
      .withAutomaticReconnect([0, 2000, 10000, 30000])
      .configureLogging(this.config.isDevelopment ? signalR.LogLevel.Debug : signalR.LogLevel.Warning)
      .build();

    // Connection event handlers
    this.gameConnection.onreconnecting(() => {
      console.log('Game hub reconnecting...');
    });

    this.gameConnection.onreconnected(() => {
      console.log('Game hub reconnected');
    });

    this.gameConnection.onclose(() => {
      console.log('Game hub connection closed');
    });

    await this.gameConnection.start();
    console.log('Connected to Game Hub');
    return this.gameConnection;
  }

  // Video Hub Connection
  async connectToVideoHub(): Promise<signalR.HubConnection> {
    if (this.videoConnection?.state === signalR.HubConnectionState.Connected) {
      return this.videoConnection;
    }

    this.videoConnection = new signalR.HubConnectionBuilder()
      .withUrl(this.config.getVideoHubUrl(), this.config.getSocketConfig())
      .withAutomaticReconnect([0, 2000, 10000, 30000])
      .configureLogging(this.config.isDevelopment ? signalR.LogLevel.Debug : signalR.LogLevel.Warning)
      .build();

    await this.videoConnection.start();
    console.log('Connected to Video Hub');
    return this.videoConnection;
  }

  // Game Hub Methods
  async createLobby(playerName: string, teamName?: string): Promise<any> {
    const connection = await this.connectToGameHub();
    return await connection.invoke('CreateLobby', { playerName, teamName });
  }

  async joinLobby(lobbyCode: string, playerName: string): Promise<any> {
    const connection = await this.connectToGameHub();
    return await connection.invoke('JoinLobby', { lobbyCode, playerName });
  }

  async setTeamReady(lobbyCode: string, ready: boolean): Promise<any> {
    const connection = await this.connectToGameHub();
    return await connection.invoke('SetTeamReady', { lobbyCode, ready });
  }

  async voteTimeframe(timeframe: number): Promise<any> {
    const connection = await this.connectToGameHub();
    return await connection.invoke('VoteTimeframe', { timeframe });
  }

  async playCard(card: any): Promise<any> {
    const connection = await this.connectToGameHub();
    return await connection.invoke('PlayCard', { card });
  }

  async selectTrump(trumpSuit: string): Promise<any> {
    const connection = await this.connectToGameHub();
    return await connection.invoke('SelectTrump', { trumpSuit });
  }

  async sendChatMessage(message: string, lobbyCode?: string): Promise<any> {
    const connection = await this.connectToGameHub();
    return await connection.invoke('SendChatMessage', { message, lobbyCode });
  }

  // Event Listeners for Game Hub
  onPlayersUpdated(callback: (data: any) => void): void {
    this.gameConnection?.on('players_updated', callback);
  }

  onGameStarted(callback: (data: any) => void): void {
    this.gameConnection?.on('game_started', callback);
  }

  onCardPlayed(callback: (data: any) => void): void {
    this.gameConnection?.on('card_played', callback);
  }

  onChatMessage(callback: (data: any) => void): void {
    this.gameConnection?.on('chat_message', callback);
  }

  // Video Hub Methods
  async joinVideoRoom(roomId: string, name: string): Promise<any> {
    const connection = await this.connectToVideoHub();
    return await connection.invoke('JoinRoom', { roomId, name });
  }

  async leaveVideoRoom(roomId: string): Promise<any> {
    const connection = await this.connectToVideoHub();
    return await connection.invoke('LeaveRoom', { roomId });
  }

  async sendVideoSignal(roomId: string, to: string, signal: any): Promise<any> {
    const connection = await this.connectToVideoHub();
    return await connection.invoke('Signal', { roomId, to, signal });
  }

  // Event Listeners for Video Hub
  onVideoUserJoined(callback: (data: any) => void): void {
    this.videoConnection?.on('user_joined', callback);
  }

  onVideoUserLeft(callback: (data: any) => void): void {
    this.videoConnection?.on('user_left', callback);
  }

  onVideoSignal(callback: (data: any) => void): void {
    this.videoConnection?.on('signal', callback);
  }

  // Cleanup
  async disconnect(): Promise<void> {
    if (this.gameConnection) {
      await this.gameConnection.stop();
      this.gameConnection = null;
    }
    if (this.videoConnection) {
      await this.videoConnection.stop();
      this.videoConnection = null;
    }
  }

  // Get connection status
  getGameConnectionState(): string {
    return this.gameConnection?.state || 'Disconnected';
  }

  getVideoConnectionState(): string {
    return this.videoConnection?.state || 'Disconnected';
  }

  // Configuration info
  getConfig() {
    return {
      environment: this.config.isDevelopment ? 'development' : 'production',
      gameHubUrl: this.config.getGameHubUrl(),
      videoHubUrl: this.config.getVideoHubUrl(),
      apiBaseUrl: this.config.getApiBaseUrl()
    };
  }
}

// Export singleton instance
export const signalRService = new SignalRService();
export default signalRService;
