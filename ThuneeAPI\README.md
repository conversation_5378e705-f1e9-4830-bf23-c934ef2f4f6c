# Thunee ASP.NET Core API with SignalR

This is the ASP.NET Core migration of the Node.js Thunee game server, using SignalR for real-time communication.

## Features

- **SignalR Hubs**: Real-time communication for game events and video signaling
- **Game Logic**: Complete game state management and rule enforcement
- **Lobby System**: Create, join, and manage game lobbies
- **Spectator Mode**: Watch live games in progress
- **Video Chat**: WebRTC signaling for video calls between players
- **Match Making**: Automatic matching of teams looking for opponents
- **Card Game Logic**: Full implementation of Thunee card game rules

## Project Structure

```
ThuneeAPI/
├── Controllers/           # REST API controllers
├── Hubs/                 # SignalR hubs
├── Models/               # Data models and DTOs
├── Services/             # Business logic services
├── Program.cs            # Application startup
└── ThuneeAPI.csproj     # Project file
```

## Prerequisites

- .NET 8.0 SDK or later
- Visual Studio 2022 or VS Code
- IIS (for production deployment)

## Development Setup

1. **Navigate to the project directory:**
   ```bash
   cd "C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI"
   ```

2. **Restore packages:**
   ```bash
   dotnet restore
   ```

3. **Build the project:**
   ```bash
   dotnet build
   ```

4. **Run the application:**
   ```bash
   dotnet run
   ```

5. **Access the application:**
   - Development: `http://localhost:5000` or `https://localhost:5001`
   - SignalR Game Hub: `http://localhost:5000/gameHub`
   - SignalR Video Hub: `http://localhost:5000/videoHub`
   - Test Client: `http://localhost:5000/test-client.html`
   - Config File: `http://localhost:5000/config.js`
   - Swagger UI: `/swagger` (development only)

## Configuration

### CORS Settings

The application is configured with two CORS policies:

- **Development**: Allows all origins (`AllowAll` policy)
- **Production**: Restricts to specific origins (`Production` policy)

Update the production origins in `Program.cs`:
```csharp
policy.WithOrigins("http://**************:96", "https://**************:96")
```

### SignalR Configuration

SignalR is configured with:
- Detailed errors enabled (development)
- 15-second keep-alive interval
- 60-second client timeout
- 30-second handshake timeout
- 100MB maximum message size

## API Endpoints

### SignalR Hubs

#### Game Hub (`/gameHub`)
- Lobby management (create, join, ready up)
- Game actions (play cards, select trump, bid)
- Special calls (Jordhi, Double, Khanak, Thunee, 4-ball)
- Spectator functionality
- Chat messaging

#### Video Hub (`/videoHub`)
- Video room management
- WebRTC signaling
- User registration

### REST Endpoints

#### Debug Endpoints
- `GET /debug/match-queue` - View match queue status
- `GET /debug/force-match?lobby1=X&lobby2=Y` - Force match between lobbies

## Client Integration

### React Frontend Changes

Update your React frontend's socket service to connect to the ASP.NET Core server:

```typescript
// In src/services/socketService.ts
const getServerUrl = () => {
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return `http://localhost:5000`; // ASP.NET Core development port
  }

  // Production environment
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = window.location.port;

  return `${protocol}//${hostname}${port ? ':' + port : ''}`;
};

// Connect to the game hub
this.socket = io(`${SOCKET_SERVER_URL}/gameHub`, {
  // ... existing options
});
```

### SignalR Client Usage

The client should emit events to the hub methods and listen for hub events:

```typescript
// Emit to hub method
socket.emit('CreateLobby', { playerName: 'John', teamName: 'Team A' }, (response) => {
  if (response.success) {
    console.log('Lobby created:', response.data);
  }
});

// Listen for hub events
socket.on('players_updated', (data) => {
  console.log('Players updated:', data);
});
```

## Deployment

### IIS Deployment

1. **Publish the application:**
   ```bash
   dotnet publish -c Release -o ./publish
   ```

2. **Copy files to IIS:**
   - Copy the `publish` folder contents to `C:\inetpub\Thunee-API\`

3. **Configure IIS:**
   - Create a new application pool targeting .NET Core
   - Create a new website pointing to the published folder
   - Ensure the application pool has appropriate permissions

4. **Configure environment:**
   - Set `ASPNETCORE_ENVIRONMENT=Production`
   - Update CORS origins for production

### Environment Variables

- `ASPNETCORE_ENVIRONMENT`: Set to `Production` for production deployment
- `ASPNETCORE_URLS`: Configure listening URLs (e.g., `http://+:80;https://+:443`)

## Migration from Node.js

### Key Differences

1. **Event Handling**: SignalR uses method calls instead of event emissions
2. **Type Safety**: Strong typing with C# models
3. **Dependency Injection**: Built-in DI container for service management
4. **Configuration**: appsettings.json instead of environment variables

### Migration Checklist

- [x] Basic project structure
- [x] SignalR hubs (Game and Video)
- [x] Core models and DTOs
- [x] Service interfaces and basic implementations
- [x] Lobby management
- [x] Game state management
- [ ] Complete game logic implementation
- [ ] Handler implementations (Jordhi, Double, Khanak, etc.)
- [ ] Spectator system
- [ ] Match making system
- [ ] Timer and turn management
- [ ] 4-ball validation logic
- [ ] Card dealing and shuffling
- [ ] Comprehensive testing

## Development Notes

### Adding New Game Features

1. **Add models** in `Models/` folder
2. **Create service interface** in `Services/`
3. **Implement service** with business logic
4. **Add hub methods** in appropriate hub
5. **Register service** in `Program.cs`
6. **Update client** to use new methods

### Event Flow

1. Client calls hub method
2. Hub validates request and calls service
3. Service processes business logic
4. Service triggers events
5. Hub listens to service events
6. Hub broadcasts to appropriate clients

## Troubleshooting

### Common Issues

1. **CORS Errors**: Check CORS policy configuration
2. **SignalR Connection Issues**: Verify hub URLs and transport settings
3. **Service Registration**: Ensure all services are registered in DI container
4. **Event Handling**: Check that event handlers are properly wired up

### Debugging

- Enable detailed errors in SignalR configuration
- Use browser developer tools to inspect SignalR traffic
- Check server logs for exceptions and errors
- Use the debug endpoints to inspect application state

## Contributing

When adding new features:

1. Follow the existing patterns for services and hubs
2. Add appropriate error handling and validation
3. Update this README with any new configuration requirements
4. Test both development and production scenarios

## License

This project is part of the Thunee card game application.
