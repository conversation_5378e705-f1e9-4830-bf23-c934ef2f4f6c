namespace ThuneeAPI.Models
{
    public class CreateLobbyRequest
    {
        public string PlayerName { get; set; } = string.Empty;
        public string? TeamName { get; set; }
        public TimeSettings? TimeSettings { get; set; }
    }

    public class JoinLobbyRequest
    {
        public string LobbyCode { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
    }

    public class UpdateTeamNameRequest
    {
        public string? LobbyCode { get; set; }
        public int TeamNumber { get; set; }
        public string TeamName { get; set; } = string.Empty;
    }

    public class SetTeamReadyRequest
    {
        public string? LobbyCode { get; set; }
        public bool Ready { get; set; }
    }

    public class StartGameRequest
    {
        public string? LobbyCode { get; set; }
    }

    public class FindMatchRequest
    {
        public string? LobbyCode { get; set; }
    }

    public class CancelFindMatchRequest
    {
        public string? LobbyCode { get; set; }
    }

    public class GameActionRequest
    {
        public string Action { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
    }

    public class VoteTimeframeRequest
    {
        public int Timeframe { get; set; }
    }

    public class PlayCardRequest
    {
        public Card Card { get; set; } = new();
    }

    public class SelectTrumpRequest
    {
        public string TrumpSuit { get; set; } = string.Empty;
    }

    public class BidRequest
    {
        public int BidAmount { get; set; }
    }

    public class PassBidRequest
    {
        // Empty request for passing a bid
    }

    public class ShuffleCardsRequest
    {
        public string ShuffleType { get; set; } = string.Empty;
    }

    public class CutCardsRequest
    {
        public int CutPosition { get; set; }
    }

    public class DealCardsRequest
    {
        public int CardsPerPlayer { get; set; }
    }

    public class CallJordhiRequest
    {
        public int Value { get; set; }
        public string JordhiSuit { get; set; } = string.Empty;
        public List<Card> JordhiCards { get; set; } = new();
    }

    public class RevealJordhiRequest
    {
        public int JordhiValue { get; set; }
        public string JordhiSuit { get; set; } = string.Empty;
        public List<Card> JordhiCards { get; set; } = new();
        public bool RevealCards { get; set; }
    }

    public class CallDoubleRequest
    {
        // Empty request for calling double
    }

    public class CallKhanakRequest
    {
        // Empty request for calling khanak
    }

    public class CallThuneeRequest
    {
        public Card FirstCard { get; set; } = new();
    }

    public class FourBallRequest
    {
        public string BallType { get; set; } = string.Empty;
        public string Option { get; set; } = string.Empty;
        public string AccusedPlayerId { get; set; } = string.Empty;
        public int HandNumber { get; set; }
    }

    public class SetDealerRequest
    {
        public string DealerId { get; set; } = string.Empty;
    }

    public class JoinSpectatorRequest
    {
        public string GameCode { get; set; } = string.Empty;
        public string SpectatorName { get; set; } = string.Empty;
    }

    public class LeaveSpectatorRequest
    {
        // Empty request for leaving spectator mode
    }

    public class VideoJoinRoomRequest
    {
        public string RoomId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class VideoLeaveRoomRequest
    {
        public string RoomId { get; set; } = string.Empty;
    }

    public class VideoSignalRequest
    {
        public string RoomId { get; set; } = string.Empty;
        public string To { get; set; } = string.Empty;
        public object Signal { get; set; } = new();
    }

    public class SendChatMessageRequest
    {
        public string Message { get; set; } = string.Empty;
        public string? LobbyCode { get; set; }
    }
}
