import { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import PlayerHand from "./components/PlayerHand";
import CollapsibleGameControls from "./components/CollapsibleGameControls";
import BottomNavigation from "./components/BottomNavigation";
import TrumpSelector from "./components/TrumpSelector";
import BurgerMenu from "./components/BurgerMenu";
import GameRules from "./components/GameRules";
import PlayTimeframeVoting from "./components/PlayTimeframeVoting";
import DealerDetermination from "./components/DealerDetermination";
import DealerIndicator from "./components/DealerIndicator";
import CardDealing from "./components/CardDealing";
import CardPassingStatus from "./components/CardPassingStatus";
import CardPassingModal from "./components/CardPassingModal";
import ThuneeCallingPrompt from "./components/ThuneeCallingPrompt";
import ShuffleButton from "./components/ShuffleButton";
import CutButton from "./components/CutButton";
import CutOptions from "./components/CutOptions";
import DealFirstFour from "./components/DealFirstFour";
import DealFinalCards from "./components/DealFinalCards";
import PlayedCardsArrangement from "./components/PlayedCardsArrangement";
import PlayerPositions from "./components/PlayerPositions";
import HandHistory from "./components/HandHistory";
import JordhiHistory from "./components/JordhiHistory";
import JordhiNotificationManager from "./components/JordhiNotificationManager";
import BiddingModal from "./components/BiddingModal";
import CardShuffleAnimation from "./components/CardShuffleAnimation";
import TurnIndicator from "./components/TurnIndicator";
// Card dealing animation removed as requested
// import CardDealingAnimation from "./components/CardDealingAnimation";
import CutAnimation from "./components/CutAnimation";
import DealerCardAnimation from "./components/DealerCardAnimation";
import PlayerCardAnimation from "./components/PlayerCardAnimation";
import Notification from "./components/Notification";
import Chat from "./components/Chat";
import VideoCall from "./components/VideoCall";
import ChatButton from "./components/ChatButton";
import HoldGameButton from "./components/HoldGameButton";
import SpectatorView from "./components/SpectatorView";
import BallResultsDisplay from "./components/BallResultsDisplay";
import StandardBallResultsDisplay from "./components/StandardBallResultsDisplay";
import KhanakBallResultsDisplay from "./components/KhanakBallResultsDisplay";
import IncorrectJordhiModal from "./components/IncorrectJordhiModal";
import FourBallResultDisplay from "./components/FourBallResultDisplay";
import NeverFollowSuitModal from "./components/NeverFollowSuitModal";
import NeverFollowSuitResultDisplay from "./components/NeverFollowSuitResultDisplay";
import JordhiRevealModal from "./components/JordhiRevealModal";
import JordhiCardsReveal from "./components/JordhiCardsReveal";
import UnderChoppedModal from "./components/UnderChoppedModal";
import UnderChoppedResultDisplay from "./components/UnderChoppedResultDisplay";
import TimeoutBallResultsDisplay from "./components/TimeoutBallResultsDisplay";
import ThuneeResultDisplay from "./components/ThuneeResultDisplay";
import GameEndDisplay from "./components/GameEndDisplay";
import { useLobbyStore } from "./store/lobbyStore";
import { useGameStore, setupGameListeners, Card } from "./store/gameStore";
import { useTimeSettingsStore } from "./store/timeSettingsStore";
import { playSound, SOUNDS } from "./utils/soundUtils";
import { useChatStore, setupChatListeners } from "./store/chatStore";
import { setupVideoCallListeners } from "./store/videoCallStore";
import { useSpectatorStore, setupSpectatorListeners } from "./store/spectatorStore";
import socketService from "./services/socketService";
import gameService from "./services/gameService";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "./components/ui/alert";
import PlayerTeamBallCards from "./components/PlayerTeamBallCards";
import OppositeTeamBallCards from "./components/OppositeTeamBallCards";
import TrumpDisplay from "./components/TrumpDisplay";
import OrientationWarning from "./components/OrientationWarning";
import EnvironmentIndicator from "./components/EnvironmentIndicator";

function App() {
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showRules, setShowRules] = useState(true); // Show rules by default when game starts
  const [showCardDealing, setShowCardDealing] = useState(false);
  const [showCutOptions, setShowCutOptions] = useState(false);
  const [showTrumpSelector, setShowTrumpSelector] = useState(false);
  const [isDealing, setIsDealing] = useState(false);
  const [showShuffleAnimation, setShowShuffleAnimation] = useState(false);
  const [shuffleType, setShuffleType] = useState<"cascade" | "riffle" | "overhand">("cascade");
  // Deal four animation removed as requested
  // const [showDealFourAnimation, setShowDealFourAnimation] = useState(false);

  // State for cut animation
  const [showCutAnimation, setShowCutAnimation] = useState(false);
  const [cutPosition, setCutPosition] = useState<string | null>(null);

  // State for card dealing animations
  const [showDealerAnimation, setShowDealerAnimation] = useState(false);
  const [showPlayerAnimation, setShowPlayerAnimation] = useState(false);
  const [dealingCardCount, setDealingCardCount] = useState<2 | 4>(4);
  // Animation for final cards removed as requested
  // const [showDealTwoAnimation, setShowDealTwoAnimation] = useState(false);
  const [showHoldGameButton, setShowHoldGameButton] = useState(false);
  const [gameHeld, setGameHeld] = useState(false);

  // Ball Results Display state
  const [showBallResults, setShowBallResults] = useState(false);
  const [showContinueGameButton, setShowContinueGameButton] = useState(false);
  const [ballResultsData, setBallResultsData] = useState<{
    doubleCallerId?: string;
    doubleCallerTeam?: 1 | 2;
    handWinnerId?: string;
    handWinnerTeam?: 1 | 2;
    handsWonByOppositeTeam?: any[];
    lastHand?: any;
    outcome?: 'success' | 'teammate_won' | 'opponent_won' | 'opposing_team_won_previous_hands';
    ballsAwarded?: number;
    winningTeam?: 1 | 2;
  }>({});

  // Khanak Ball Results Display state
  const [showKhanakBallResults, setShowKhanakBallResults] = useState(false);
  const [khanakBallResultsData, setKhanakBallResultsData] = useState<{
    khanakCallerId?: string;
    khanakCallerTeam?: 1 | 2;
    handWinnerId?: string;
    handWinnerTeam?: 1 | 2;
    opposingTeamHands?: any[];
    threshold?: number;
    opposingTeamPoints?: number;
    teamJordhiPoints?: number;
    opposingTeamJordhiPoints?: number;
    outcome?: 'points_below_threshold' | 'points_above_threshold' | 'last_hand_lost' | 'opposing_team_no_hands' | 'no_jordhi_calls';
    ballsAwarded?: number;
    winningTeam?: 1 | 2;
  }>({});

  // Four Ball Result Display state
  const [showFourBallResult, setShowFourBallResult] = useState(false);
  const [fourBallResultData, setFourBallResultData] = useState<{
    ballType: string;
    option: string;
    targetPlayer: string;
    targetTeam: 1 | 2;
    isValidJordhi: boolean;
    jordhiValue: number;
    jordhiCards?: Array<{ suit: string, value: string }>;
    winningTeam: 1 | 2;
    ballsAwarded: number;
  } | null>(null);

  // Never Follow Suit Result Display state
  const [showNeverFollowSuitResult, setShowNeverFollowSuitResult] = useState(false);
  const [neverFollowSuitResultData, setNeverFollowSuitResultData] = useState<{
    ballType: string;
    option: string;
    targetPlayer: string;
    targetPlayerName: string;
    targetTeam: 1 | 2;
    accuserId: string;
    accuserName: string;
    accuserTeam: 1 | 2;
    handNumber: number;
    leadSuit: string;
    cardPlayed: {
      suit: string;
      value: string;
    };
    handBeforePlaying: Array<{
      suit: string;
      value: string;
    }>;
    isValid: boolean;
    winningTeam: 1 | 2;
    ballsAwarded: number;
  } | null>(null);

  // Under Chopped Result Display state
  const [underChoppedResultOpen, setUnderChoppedResultOpen] = useState(false);
  const [underChoppedResult, setUnderChoppedResult] = useState<{
    ballType: string;
    option: string;
    targetPlayer: string;
    targetPlayerName: string;
    targetTeam: 1 | 2;
    accuserId: string;
    accuserName: string;
    accuserTeam: 1 | 2;
    handNumber: number;
    trumpSuit: string;
    cardPlayed: {
      suit: string;
      value: string;
    };
    handBeforePlaying: Array<{
      suit: string;
      value: string;
    }>;
    isValid: boolean;
    winningTeam: 1 | 2;
    ballsAwarded: number;
    customTrumpRanking: string[];
  } | null>(null);

  // Thunee Result Display state
  const [showThuneeResult, setShowThuneeResult] = useState(false);
  const [thuneeResultData, setThuneeResultData] = useState<any>(null);

  // Jordhi Reveal Modal state
  const [showJordhiRevealModal, setShowJordhiRevealModal] = useState(false);
  const [jordhiRevealData, setJordhiRevealData] = useState<{
    playerId: string;
    playerName: string;
    playerTeam: 1 | 2;
    value: number;
    jordhiSuit: string;
    jordhiCards: Array<{ suit: string, value: string }>;
  } | null>(null);

  // Jordhi Cards Reveal state (for all players)
  const [showJordhiCardsReveal, setShowJordhiCardsReveal] = useState(false);
  const [jordhiCardsRevealData, setJordhiCardsRevealData] = useState<{
    playerName: string;
    playerTeam: 1 | 2;
    value: number;
    jordhiSuit: string;
    cardsRevealed: boolean;
    jordhiCards?: Array<{ suit: string, value: string }>;
  } | null>(null);

  // Standard Ball Results Display state (for normal ball completion without Double/Thunee/Khanak)
  const [showStandardBallResults, setShowStandardBallResults] = useState(false);
  const [standardBallResultsData, setStandardBallResultsData] = useState<{
    handsWonByNonTrumpingTeam: any[];
    targetScore: number;
    initialTargetScore: number;
    jordhiAdjustments: {
      nonTrumpingTeam: number;
      trumpingTeam: number;
    };
    finalHandAdjustment: number;
    nonTrumpingTeamPoints: number;
    trumpingTeam: 1 | 2;
    winningTeam: 1 | 2;
    ballsAwarded: number;
    isCallAndLost?: boolean;
  }>({
    handsWonByNonTrumpingTeam: [],
    targetScore: 105,
    initialTargetScore: 105,
    jordhiAdjustments: {
      nonTrumpingTeam: 0,
      trumpingTeam: 0
    },
    finalHandAdjustment: 0,
    nonTrumpingTeamPoints: 0,
    trumpingTeam: 1,
    winningTeam: 1,
    ballsAwarded: 1
  });

  // Timeout Ball Results Display state
  const [showTimeoutBallResults, setShowTimeoutBallResults] = useState(false);
  const [timeoutBallResultsData, setTimeoutBallResultsData] = useState<{
    timedOutPlayer: {
      id: string;
      name: string;
      team: 1 | 2;
    };
    opposingTeam: 1 | 2;
    ballsAwarded: number;
  }>({
    timedOutPlayer: {
      id: '',
      name: '',
      team: 1
    },
    opposingTeam: 2,
    ballsAwarded: 1
  });

  // Game End Display state
  const [showGameEndDisplay, setShowGameEndDisplay] = useState(false);

  // Thunee calling logic state
  const [showThuneePrompt, setShowThuneePrompt] = useState(false);
  const [thuneeCallingStage, setThuneeCallingStage] = useState<"trumper" | "first-remaining" | "last-remaining" | null>(null);
  const [thuneeCallingDuration, setThuneeCallingDuration] = useState(5);
  const [gamePhase, setGamePhase] = useState<
    "rules" | "play-timeframe-voting" | "dealer-determination" | "shuffle" | "cut" | "deal-first-four" | "review-cards" | "bidding" | "select-trump" | "waiting-for-final-cards" | "gameplay"
  >("rules");
  const navigate = useNavigate();

  // Get state from the lobby store
  const {
    lobbyCode,
    isConnected,
    players: lobbyPlayers,
    teamNames,
  } = useLobbyStore();

  // Get state and actions from the game store
  const {
    players,
    hand,
    initialHand,
    isTrumpSelector,
    isDealer,
    gameEnded,
    error: gameError,
    updateGameState,
  } = useGameStore();

  // Get time settings
  const { settings: timeSettings } = useTimeSettingsStore();

  // Function to check for duplicate cards
  const hasDuplicates = (cards: Card[]): boolean => {
    const cardKeys = new Set<string>();
    for (const card of cards) {
      const key = `${card.value}_${card.suit}`;
      if (cardKeys.has(key)) {
        console.error(`Duplicate card found: ${card.value} of ${card.suit}`);
        return true;
      }
      cardKeys.add(key);
    }
    return false;
  };

  // Function to remove duplicates while preserving order
  const removeDuplicates = (cards: Card[]): Card[] => {
    const uniqueCards: Card[] = [];
    const cardKeys = new Set<string>();

    for (const card of cards) {
      const key = `${card.value}_${card.suit}`;
      if (!cardKeys.has(key)) {
        cardKeys.add(key);
        uniqueCards.push(card);
      } else {
        console.warn(`Skipping duplicate card: ${card.value} of ${card.suit}`);
      }
    }

    return uniqueCards;
  };

  // Track the last processed hand to prevent infinite loops
  const lastProcessedHandRef = useRef<string>("");

  // Global card tracker removed as requested

  // Debug logging for game phase and handle card validation
  useEffect(() => {
    console.log("Current game phase:", gamePhase);
    console.log("Is dealer:", isDealer);

    // When entering gameplay phase, ensure we have all 6 cards and no duplicates
    if (gamePhase === "gameplay") {
      console.log("Entering gameplay phase with hand:", hand);

      // Create a string representation of the current hand for comparison
      const handString = JSON.stringify(hand.map(card => `${card.value}_${card.suit}`).sort());

      // Only process if this hand hasn't been processed before
      if (handString !== lastProcessedHandRef.current) {
        lastProcessedHandRef.current = handString;

        // Check for duplicates in current hand
        if (hasDuplicates(hand)) {
          console.warn("Duplicates found in hand, removing duplicates");
          const uniqueCards = removeDuplicates(hand);
          console.log("Unique cards in hand:", uniqueCards);
          updateGameState({ hand: uniqueCards });
        }
        // If we still have fewer than 6 cards, try to combine with initialHand
        else if (hand.length < 6 && initialHand.length > 0) {
          console.log("Hand has fewer than 6 cards in gameplay phase, using initialHand + any new cards");
          // Combine initialHand with any new cards that aren't in initialHand
          const newCards = hand.filter(card =>
            !initialHand.some(initCard => initCard.id === card.id)
          );
          console.log("New cards not in initialHand:", newCards);

          if (newCards.length > 0) {
            const allCards = [...initialHand, ...newCards];

            // Check for duplicates in combined cards
            if (hasDuplicates(allCards)) {
              console.warn("Duplicates found in combined cards, removing duplicates");
              const uniqueCards = removeDuplicates(allCards);
              console.log("Unique combined cards:", uniqueCards);
              updateGameState({ hand: uniqueCards });
            } else {
              console.log("Combined all cards:", allCards);
              updateGameState({ hand: allCards });
            }
          }
        }
      }
    }
  }, [gamePhase, isDealer, hand, initialHand, updateGameState]);

  // Get spectator state
  const { isSpectator, gameCode, spectatorName, gameLobby } = useSpectatorStore();

  // Log spectator state for debugging
  useEffect(() => {
    console.log('App component - isSpectator:', isSpectator);
    console.log('App component - gameCode:', gameCode);
    console.log('App component - spectatorName:', spectatorName);
    console.log('App component - gameLobby:', gameLobby);
  }, [isSpectator, gameCode, spectatorName, gameLobby]);

  // Show game end display when game ends
  useEffect(() => {
    if (gameEnded) {
      console.log('Game has ended, showing game end display');
      setShowGameEndDisplay(true);
    }
  }, [gameEnded]);

  // Function to handle immediate animation start when deal button is clicked
  const handleDealStart = () => {
    console.log("Deal button clicked - starting animation immediately");

    // Show the appropriate card dealing animation based on whether this player is the dealer
    if (isDealer) {
      console.log("Showing dealer animation for first 4 cards immediately");
      setDealingCardCount(4);
      setShowDealerAnimation(true);
    } else {
      console.log("Showing player animation for first 4 cards immediately");
      setDealingCardCount(4);
      setShowPlayerAnimation(true);
    }
  };

  // Function to handle immediate animation start when final deal button is clicked
  const handleFinalDealStart = () => {
    console.log("Final deal button clicked - starting animation immediately");

    // Show the appropriate card dealing animation based on whether this player is the dealer
    if (isDealer) {
      console.log("Showing dealer animation for final 2 cards immediately");
      setDealingCardCount(2);
      setShowDealerAnimation(true);
    } else {
      console.log("Showing player animation for final 2 cards immediately");
      setDealingCardCount(2);
      setShowPlayerAnimation(true);
    }
  };

  // Set up game listeners when component mounts
  useEffect(() => {
    setupGameListeners();
    setupChatListeners();
    setupVideoCallListeners();
    setupSpectatorListeners();

    // Listen for game phase updates from the server
    const handleGamePhaseUpdated = (data: {
      phase: "rules" | "play-timeframe-voting" | "dealer-determination" | "shuffle" | "cut" | "deal-first-four" | "review-cards" | "bidding" | "select-trump" | "waiting-for-final-cards" | "gameplay";
      players?: any[]; // Player positions may be included
    }) => {
      console.log("Game phase updated:", data.phase);
      setGamePhase(data.phase);

      // If players with positions are included, update the game state
      if (data.players && data.players.length > 0) {
        console.log("Updating player positions from game_phase_updated event:",
          data.players.map(p => `${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`)
        );

        // Update the game store with the positioned players
        const { updateGameState } = useGameStore.getState();
        updateGameState({
          players: data.players
        });
      }

      // If transitioning to play-timeframe-voting phase
      if (data.phase === "play-timeframe-voting") {
        console.log("Transitioning to play timeframe voting phase");
        setShowRules(false); // Hide rules if they're showing
      }

      // If transitioning to dealer-determination phase, make sure we show the dealer determination component
      if (data.phase === "dealer-determination") {
        console.log("Transitioning to dealer determination phase");
        setShowRules(false); // Hide rules if they're showing
        // The dealer determination component will be shown based on the gamePhase state
      }
    };

    // Listen for shuffle animation event
    const handleShuffleAnimation = (data: { shuffleType: "cascade" | "riffle" | "overhand", dealerId: string }) => {
      console.log(`Shuffle animation with ${data.shuffleType} shuffle by dealer ${data.dealerId}`);
      // Store the shuffle type in state to use it in the animation
      setShuffleType(data.shuffleType);
      // Show the shuffle animation for all players
      setShowShuffleAnimation(true);

      // If this player is the dealer, update their UI state
      if (socketService.getSocketId() === data.dealerId) {
        // Update the dealer's UI to show they are shuffling
        const dealerButton = document.querySelector('.dealer-button') as HTMLButtonElement;
        if (dealerButton) {
          dealerButton.disabled = true;
        }
      }
    };

    // Listen for shuffle complete event
    const handleShuffleComplete = () => {
      console.log("Shuffle complete");
      // Hide the shuffle animation for all players
      setShowShuffleAnimation(false);
      // The animation will automatically transition to the cut phase when complete
      setGamePhase("cut");

      // If this player is the dealer, update their UI state
      if (isDealer) {
        // Update the dealer's UI to show they are done shuffling
        const dealerButton = document.querySelector('.dealer-button') as HTMLButtonElement;
        if (dealerButton) {
          dealerButton.disabled = false;
        }
      }
    };

    // Listen for cut request event
    const handleCutRequested = (data: { playerId: string }) => {
      console.log("Cut requested for player:", data.playerId);
      const currentPlayerId = socketService.getSocketId();

      if (data.playerId === currentPlayerId) {
        console.log("This player needs to cut the deck");
        setShowCutOptions(true);

        // Log to confirm the cut options are being shown
        console.log("Setting showCutOptions to true for player", currentPlayerId);
      } else {
        console.log("This player does not need to cut the deck", currentPlayerId);
      }
    };

    // Listen for cut complete event
    const handleCutComplete = (data: { cut: boolean, position?: string, playerId: string }) => {
      console.log(`Cut complete, player ${data.playerId} ${data.cut ? `cut at ${data.position}` : 'did not cut'} the deck`);

      // Hide cut options for the player who was cutting
      if (socketService.getSocketId() === data.playerId) {
        setShowCutOptions(false);
      }

      // Show cut animation to all players
      if (data.cut && data.position) {
        // Show the cut animation to all players
        setShowCutAnimation(true);
        setCutPosition(data.position);

        console.log(`Showing cut animation at position: ${data.position}`);

        // After animation completes, move to next phase
        setTimeout(() => {
          setShowCutAnimation(false);
          setGamePhase("deal-first-four");
        }, 1500); // Animation duration
      } else {
        // No cut, just move to next phase
        setGamePhase("deal-first-four");
      }
    };

    // Listen for trump selector assigned event
    const handleTrumpSelectorAssigned = (data: { playerId: string }) => {
      console.log("Trump selector assigned:", data.playerId);
      const currentPlayerId = socketService.getSocketId();

      // Update the game state to reflect who is the trump selector
      const isTrumpSelector = data.playerId === currentPlayerId;
      console.log(`Current player (${currentPlayerId}) is trump selector: ${isTrumpSelector}`);
      updateGameState({ isTrumpSelector });

      // Update the players array to mark the trump selector
      const updatedPlayers = players.map(player => {
        const isThisTrumpSelector = player.id === data.playerId;
        console.log(`Player ${player.name} (${player.id}) is trump selector: ${isThisTrumpSelector}`);
        return {
          ...player,
          isTrumpSelector: isThisTrumpSelector
        };
      });
      updateGameState({ players: updatedPlayers });
    };

    // Listen for first four cards dealt event
    const handleFirstFourDealt = () => {
      console.log("First four cards dealt");

      // Animation should already be running from handleDealStart
      // If for some reason it's not, start it now
      if (!showDealerAnimation && !showPlayerAnimation) {
        console.log("Animation not running, starting it now");
        handleDealStart();
      }

      // First show a message to all players to look at their cards
      setGamePhase("review-cards");

      // After animation completes (3 seconds), move to bidding phase
      // The animation will take about 2.5 seconds to complete
      setTimeout(() => {
        setGamePhase("bidding");
        console.log("Moving to bidding phase - showing bidding modal to all players");

        // Make sure the bidding modal is shown to all players
        const currentPlayerId = socketService.getSocketId();
        console.log("Current player ID:", currentPlayerId);
      }, 3500);
    };

    // Listen for bidding complete event
    const handleBiddingComplete = (data: { trumpSelector: string, finalBid: number }) => {
      console.log(`Bidding complete. Trump selector: ${data.trumpSelector}, Final bid: ${data.finalBid}`);

      // Move to the next phase

      // Move to trump selection phase
      setGamePhase("select-trump");

      // Get the current state to ensure we have the latest value
      const currentState = useGameStore.getState();
      const currentIsTrumpSelector = currentState.isTrumpSelector;

      // If the current player is the trump selector, show the trump selector
      if (currentIsTrumpSelector) {
        console.log("This player is the trump selector");
        setShowTrumpSelector(true);
      } else {
        console.log("This player is not the trump selector");
      }
    };

    // Listen for trump selected event
    const handleTrumpSelected = (data: { suit: string, isLastCard: boolean, selectedBy: string }) => {
      console.log(`Trump selected: ${data.isLastCard ? 'Last Card' : data.suit}`);
      setShowTrumpSelector(false);

      // Update the trump suit in the game store
      updateGameState({
        trumpSuit: data.suit,
        isLastCard: data.isLastCard
      });

      // Wait for the dealer to deal the final cards
      // The game phase will be updated after the final cards are dealt
      setGamePhase("waiting-for-final-cards");

      // If this player is the dealer, they will see the deal final cards button
      if (isDealer) {
        console.log("This player is the dealer, will see deal final cards button");
      }
    };

    // Listen for first player determined event
    const handleFirstPlayerDetermined = (data: {
      playerId: string,
      playerName: string,
      playerTeam: number,
      message: string
    }) => {
      console.log(`First player determined: ${data.playerName} (${data.playerId}) - Team ${data.playerTeam}`);
      console.log(`Message: ${data.message}`);

      // Update the game state with the first player information
      const currentPlayerId = socketService.getSocketId();
      const isCurrentTurn = data.playerId === currentPlayerId;

      updateGameState({
        currentTurn: data.playerId,
        isCurrentTurn
      });

      console.log(`Is current player's turn: ${isCurrentTurn}`);
    };

    // Listen for deal final cards request
    const handleDealFinalCardsRequest = () => {
      console.log("Dealer needs to deal final cards");

      // Only the dealer should handle this
      if (isDealer) {
        console.log("This player is the dealer, will see deal final cards button");
        // The button is already shown based on gamePhase and isDealer
      } else {
        console.log("This player is not the dealer, waiting for dealer to deal final cards");
      }
    };

    // Listen for final cards dealt event
    const handleFinalCardsDealt = (data: { nextPlayerId: string, trumpSuit: string, isLastCard: boolean }) => {
      console.log(`Final cards dealt. Next player: ${data.nextPlayerId}, Trump: ${data.trumpSuit}`);

      // Show the appropriate card dealing animation based on whether this player is the dealer
      if (isDealer) {
        console.log("Showing dealer animation for final 2 cards");
        setDealingCardCount(2);
        setShowDealerAnimation(true);
      } else {
        console.log("Showing player animation for final 2 cards");
        setDealingCardCount(2);
        setShowPlayerAnimation(true);
      }

      // Give time for the animation to complete before proceeding
      setTimeout(() => {
        // Update game state with the new cards
        console.log("Animation complete, updating game state with new cards");
      }, 3000);

      // Skip the old deal two animation
      // setShowDealTwoAnimation(true);

      // Move to gameplay phase after final cards are dealt
      console.log('Moving to gameplay phase with hand:', hand);

      // Function to request missing cards
      const requestMissingCards = async () => {
        console.log('Requesting missing cards from server...');
        return socketService.sendGameAction("request_missing_cards", {}).catch(err => {
          console.error('Error requesting missing cards:', err);
        });
      };

      // Function to check if we have all cards and proceed to gameplay
      const checkCardsAndProceed = () => {
        const currentHand = useGameStore.getState().hand;
        console.log('Checking hand before gameplay:', currentHand);

        if (currentHand.length < 6) {
          console.warn(`Warning: Player only has ${currentHand.length} cards before entering gameplay phase`);
          return false;
        } else {
          console.log('Player has all 6 cards, proceeding to gameplay phase');
          return true;
        }
      };

      // Add a longer delay to ensure all cards have been received
      setTimeout(async () => {
        // First check
        if (!checkCardsAndProceed()) {
          // First attempt to request missing cards
          await requestMissingCards();

          // Wait and check again
          setTimeout(async () => {
            if (!checkCardsAndProceed()) {
              // Second attempt to request missing cards
              await requestMissingCards();

              // Final check and proceed regardless
              setTimeout(() => {
                const finalHand = useGameStore.getState().hand;
                console.log(`Final check: Player has ${finalHand.length} cards`);
                setGamePhase("gameplay");

                // Check if this player is the Trumper
                const currentState = useGameStore.getState();
                const isTrumper = currentState.isTrumpSelector;

                if (isTrumper) {
                  // If this player is the Trumper, show the Thunee calling prompt directly
                  console.log("This player is the Trumper, showing Thunee calling prompt");
                  setThuneeCallingStage("trumper");
                  setThuneeCallingDuration(5);
                  setShowThuneePrompt(true);
                  setGameHeld(true);
                } else {
                  // For non-Trumpers, don't show the Hold Game button initially
                  // The Trumper will get the first opportunity, and after they pass,
                  // the Hold Game button will be shown to non-Trumpers via the trumper_opportunity_complete event
                  console.log("This player is not the Trumper, waiting for Trumper's decision");
                  setShowHoldGameButton(false);
                  setGameHeld(true);
                }
              }, 2000);
            } else {
              setGamePhase("gameplay");

              // Check if this player is the Trumper
              const currentState = useGameStore.getState();
              const isTrumper = currentState.isTrumpSelector;

              if (isTrumper) {
                // If this player is the Trumper, show the Thunee calling prompt directly
                console.log("This player is the Trumper, showing Thunee calling prompt");
                setThuneeCallingStage("trumper");
                setThuneeCallingDuration(5);
                setShowThuneePrompt(true);
                setGameHeld(true);
              } else {
                // For non-Trumpers, don't show the Hold Game button initially
                // The Trumper will get the first opportunity, and after they pass,
                // the Hold Game button will be shown to non-Trumpers via the trumper_opportunity_complete event
                console.log("This player is not the Trumper, waiting for Trumper's decision");
                setShowHoldGameButton(false);
                setGameHeld(true);
              }
            }
          }, 2000);
        } else {
          setGamePhase("gameplay");

          // Check if this player is the Trumper
          const currentState = useGameStore.getState();
          const isTrumper = currentState.isTrumpSelector;

          if (isTrumper) {
            // If this player is the Trumper, show the Thunee calling prompt directly
            console.log("This player is the Trumper, showing Thunee calling prompt");
            setThuneeCallingStage("trumper");
            setThuneeCallingDuration(5);
            setShowThuneePrompt(true);
            setGameHeld(true);
          } else {
            // For non-Trumpers, don't show the Hold Game button initially
            // The Trumper will get the first opportunity, and after they pass,
            // the Hold Game button will be shown to non-Trumpers via the trumper_opportunity_complete event
            console.log("This player is not the Trumper, waiting for Trumper's decision");
            setShowHoldGameButton(false);
            setGameHeld(true);
          }
        }
      }, 3000);

      // Update the game state with the next player and trump information
      const currentPlayerId = socketService.getSocketId();
      const isCurrentTurn = data.nextPlayerId === currentPlayerId;

      updateGameState({
        currentTurn: data.nextPlayerId,
        isCurrentTurn,
        trumpSuit: data.trumpSuit,
        isLastCard: data.isLastCard
      });

      console.log(`Is current player's turn: ${isCurrentTurn}`);
    };

    // Listen for cards dealt event
    const handleCardsDealt = (data: { dealerId: string }) => {
      console.log(`Cards dealt by dealer ${data.dealerId}`);
      // Reset the dealing state
      setIsDealing(false);
    };

    // Listen for game held event
    const handleGameHeld = (data: {
      playerId: string,
      playerName: string,
      duration: number,
      playerRole?: "trumper" | "first-remaining" | "last-remaining"
    }) => {
      console.log(`Game held by ${data.playerName} for ${data.duration} seconds. Role: ${data.playerRole || 'unknown'}`);

      // Enable the Call button during the hold period
      setGameHeld(true);

      // Set the Thunee calling stage based on the player role
      if (data.playerRole) {
        setThuneeCallingStage(data.playerRole);
        setThuneeCallingDuration(data.duration);

        // For all players, hide the Hold Game button during this time
        // This prevents multiple players from trying to hold the game simultaneously
        setShowHoldGameButton(false);
      }

      // Show notification that the game is being held
      const notification = {
        title: 'Game Paused',
        message: `${data.playerName} has paused the game for ${data.duration} seconds.\nPlayers can call Thunee, Royal Thunee, Double, or Khanuck during this time.`,
        type: 'info' as const,
        duration: data.duration * 1000 // Convert seconds to milliseconds
      };

      // Dispatch notification event
      const notificationEvent = new CustomEvent('show-notification', {
        detail: notification
      });
      window.dispatchEvent(notificationEvent);

      // Log the game state for debugging
      console.log('Game state during hold:', {
        gameHeld: true,
        showHoldGameButton: true,
        gamePhase,
        thuneeCallingStage: data.playerRole || null,
        thuneeCallingDuration: data.duration
      });
    };

    // Listen for game hold complete event
    const handleGameHoldComplete = (data: {
      playerId: string,
      playerName: string,
      nextStage?: "trumper" | "first-remaining" | "last-remaining" | null
    }) => {
      console.log(`Game hold by ${data.playerName} is complete. Next stage: ${data.nextStage || 'none'}`);

      // Reset the current Thunee prompt
      setShowThuneePrompt(false);

      // If there's a next stage, set it up
      if (data.nextStage) {
        setThuneeCallingStage(data.nextStage);

        // Set the duration based on the stage using time settings
        const duration = data.nextStage === "trumper" ? timeSettings.thuneeCallingDurations.trumper :
                        data.nextStage === "first-remaining" ? timeSettings.thuneeCallingDurations.firstRemaining :
                        timeSettings.thuneeCallingDurations.lastRemaining;
        setThuneeCallingDuration(duration);

        // Keep the game held state active for the next stage
        setGameHeld(true);

        // If the next stage is first-remaining, show the Hold Game button to all non-Trumper players
        if (data.nextStage === "first-remaining") {
          const isTrumper = useGameStore.getState().isTrumpSelector;

          // Only show the Hold Game button to non-Trumper players
          if (!isTrumper) {
            console.log("Showing Hold Game button to non-Trumper player for first-remaining stage");
            setShowHoldGameButton(true);
            // Set the player role to first-remaining
            setThuneeCallingStage("first-remaining");
          }
        }
        // If the next stage is last-remaining, show the Hold Game button to the remaining players
        else if (data.nextStage === "last-remaining") {
          const currentPlayerId = socketService.getSocketId();
          const isTrumper = useGameStore.getState().isTrumpSelector;

          // Only show the Hold Game button to non-Trumper players who haven't had a chance yet
          if (!isTrumper && currentPlayerId !== data.playerId) {
            console.log("Showing Hold Game button to remaining players for last-remaining stage");
            setShowHoldGameButton(true);
            // Set the player role to last-remaining
            setThuneeCallingStage("last-remaining");
          }
        }
      } else {
        // If there's no next stage, disable the Call button and hide the Hold Game button
        setGameHeld(false);
        setShowHoldGameButton(false);
        setThuneeCallingStage(null);

        // Show notification that the game hold is complete
        const notification = {
          title: 'Game Resumed',
          message: `The game has been resumed. Play can now continue.`,
          type: 'success' as const,
          duration: 3000
        };

        // Dispatch notification event
        const notificationEvent = new CustomEvent('show-notification', {
          detail: notification
        });
        window.dispatchEvent(notificationEvent);
      }

      // Log the game state for debugging
      console.log('Game state after hold complete:', {
        gameHeld: data.nextStage !== null,
        showHoldGameButton: data.nextStage !== null,
        thuneeCallingStage: data.nextStage,
        gamePhase
      });
    };

    // Listen for thunee called event
    const handleThuneeCall = (data: { playerId: string, playerName: string, playerTeam: number, isFirstPlayer: boolean }) => {
      console.log(`Thunee called by ${data.playerName} (Team ${data.playerTeam})`);

      // Show notification that Thunee has been called
      const notification = {
        title: 'Thunee Called!',
        message: `${data.playerName} (Team ${data.playerTeam}) has called Thunee!\nThe first card played will become trump and they must win all hands.`,
        type: 'warning' as const,
        duration: 5000
      };

      // Dispatch notification event
      const notificationEvent = new CustomEvent('show-notification', {
        detail: notification
      });
      window.dispatchEvent(notificationEvent);

      // Update game state to reflect that Thunee has been called
      updateGameState({
        thuneePlayerId: data.playerId,
        thuneePlayerName: data.playerName,
        thuneePlayerTeam: data.playerTeam,
        isRoyalThunee: false
      });
    };

    // Listen for royal thunee called event
    const handleRoyalThuneeCall = (data: { playerId: string, playerName: string, playerTeam: number, isFirstPlayer: boolean }) => {
      console.log(`Royal Thunee called by ${data.playerName} (Team ${data.playerTeam})`);

      // Show notification that Royal Thunee has been called
      const notification = {
        title: 'Royal Thunee Called!',
        message: `${data.playerName} (Team ${data.playerTeam}) has called Royal Thunee!\nThe first card played will become trump and they must win all hands. Card rankings are reversed - lowest card wins!`,
        type: 'warning' as const,
        duration: 5000
      };

      // Dispatch notification event
      const notificationEvent = new CustomEvent('show-notification', {
        detail: notification
      });
      window.dispatchEvent(notificationEvent);

      // Update game state to reflect that Royal Thunee has been called
      updateGameState({
        thuneePlayerId: data.playerId,
        thuneePlayerName: data.playerName,
        thuneePlayerTeam: data.playerTeam,
        isRoyalThunee: true
      });
    };

    // Listen for thunee success event
    const handleThuneeSuccess = (data: {
      thuneePlayerId: string,
      thuneePlayerName: string,
      thuneePlayerTeam: number,
      ballsAwarded: number,
      ballScores: { team1: number, team2: number },
      handsPlayed?: any[],
      allHandsWon?: boolean,
      totalHands?: number,
      trumpSuit?: string,
      instantResult?: boolean
    }) => {
      console.log(`Thunee success by ${data.thuneePlayerName} (Team ${data.thuneePlayerTeam})`);

      // Show notification that Thunee was successful
      const notification = {
        title: 'Thunee Success!',
        message: `${data.thuneePlayerName} (Team ${data.thuneePlayerTeam}) successfully won all hands with Thunee!\nTeam ${data.thuneePlayerTeam} has been awarded ${data.ballsAwarded} balls.`,
        type: 'success' as const,
        duration: 8000
      };

      // Dispatch notification event
      const notificationEvent = new CustomEvent('show-notification', {
        detail: notification
      });
      window.dispatchEvent(notificationEvent);

      // Update game state to reflect the Thunee success
      updateGameState({
        thuneePlayerId: null,
        thuneePlayerName: null,
        thuneePlayerTeam: null,
        isRoyalThunee: false,
        ballScores: data.ballScores
      });

      // Show the comprehensive Thunee result display
      setThuneeResultData({
        success: true,
        thuneePlayerId: data.thuneePlayerId,
        thuneePlayerName: data.thuneePlayerName,
        thuneePlayerTeam: data.thuneePlayerTeam,
        ballsAwarded: data.ballsAwarded,
        winningTeam: data.thuneePlayerTeam,
        ballScores: data.ballScores,
        handsPlayed: data.handsPlayed || [],
        allHandsWon: data.allHandsWon,
        totalHands: data.totalHands || 6,
        trumpSuit: data.trumpSuit,
        instantResult: data.instantResult || false
      });
      setShowThuneeResult(true);
    };

    // Listen for thunee failure event
    const handleThuneeFailure = (data: {
      thuneePlayerId: string,
      thuneePlayerName: string,
      thuneePlayerTeam: number,
      winningPlayerId: string,
      winningPlayerName: string,
      winningPlayerTeam: number,
      isPartner: boolean,
      ballsAwarded: number,
      winningTeam: number,
      ballScores: { team1: number, team2: number },
      handsPlayed?: any[],
      currentHand?: {
        id: number,
        cards: any[],
        winner: any,
        winningCard: any,
        winReason: string
      },
      caughtInHand?: number,
      caughtByCard?: any,
      instantResult?: boolean,
      trumpSuit?: string
    }) => {
      console.log(`Thunee failure by ${data.thuneePlayerName} (Team ${data.thuneePlayerTeam})`);

      // Show notification that Thunee failed
      const handText = data.caughtInHand ? ` in hand ${data.caughtInHand}` : '';
      const cardText = data.caughtByCard ? ` with ${data.caughtByCard.value} of ${data.caughtByCard.suit}` : '';

      const notification = {
        title: 'Thunee Failed!',
        message: data.isPartner
          ? `${data.thuneePlayerName}'s partner ${data.winningPlayerName} won${handText}${cardText}!\nTeam ${data.winningTeam} has been awarded ${data.ballsAwarded} balls.`
          : `${data.thuneePlayerName} (Team ${data.thuneePlayerTeam}) was caught by ${data.winningPlayerName} (Team ${data.winningPlayerTeam})${handText}${cardText}!\nTeam ${data.winningTeam} has been awarded ${data.ballsAwarded} balls.`,
        type: 'error' as const,
        duration: 10000
      };

      // Dispatch notification event
      const notificationEvent = new CustomEvent('show-notification', {
        detail: notification
      });
      window.dispatchEvent(notificationEvent);

      // Update game state to reflect the Thunee failure
      updateGameState({
        thuneePlayerId: null,
        thuneePlayerName: null,
        thuneePlayerTeam: null,
        isRoyalThunee: false,
        ballScores: data.ballScores
      });

      // Show the comprehensive Thunee result display
      setThuneeResultData({
        success: false,
        thuneePlayerId: data.thuneePlayerId,
        thuneePlayerName: data.thuneePlayerName,
        thuneePlayerTeam: data.thuneePlayerTeam,
        winningPlayerId: data.winningPlayerId,
        winningPlayerName: data.winningPlayerName,
        winningPlayerTeam: data.winningPlayerTeam,
        isPartner: data.isPartner,
        ballsAwarded: data.ballsAwarded,
        winningTeam: data.winningTeam,
        ballScores: data.ballScores,
        handsPlayed: data.handsPlayed || [],
        currentHand: data.currentHand,
        caughtInHand: data.caughtInHand,
        caughtByCard: data.caughtByCard,
        instantResult: data.instantResult,
        trumpSuit: data.trumpSuit
      });
      setShowThuneeResult(true);
    };

    // Listen for double called event
    const handleDoubleCall = (data: {
      playerId: string,
      playerName: string,
      playerTeam: number,
      isValid?: boolean,
      reason?: string
    }) => {
      console.log(`Double called by ${data.playerName} (Team ${data.playerTeam}). Valid: ${data.isValid !== false}`);

      // Show notification that Double has been called
      const notification = {
        title: data.isValid === false ? 'Invalid Double Call!' : 'Double Called!',
        message: data.isValid === false
          ? `${data.playerName} (Team ${data.playerTeam}) tried to call Double but it was invalid.\nReason: ${data.reason}`
          : `${data.playerName} (Team ${data.playerTeam}) has called Double!\nIf they win the last hand, they will be awarded 2 balls. If they lose, the opposing team gets 4 balls.`,
        type: data.isValid === false ? 'error' : 'info',
        duration: 5000
      };

      // Dispatch notification event
      const notificationEvent = new CustomEvent('show-notification', {
        detail: notification
      });
      window.dispatchEvent(notificationEvent);
    };

    // Listen for double outcome event
    const handleDoubleOutcome = (data: {
      doubleCallerId: string,
      doubleCallerName: string,
      doubleCallerTeam: number,
      handWinnerId: string,
      handWinnerName: string,
      handWinnerTeam: number,
      ballsAwarded: number,
      winningTeam: number,
      outcome: string,
      ballScores: { team1: number, team2: number },
      opposingTeamHasWonHands?: boolean,
      opposingTeamHands?: any[],
      lastHand?: any
    }) => {
      console.log(`Double outcome: ${data.outcome}. Balls awarded: ${data.ballsAwarded} to Team ${data.winningTeam}`);

      let title = '';
      let message = '';
      let type: 'success' | 'error' | 'info' = 'info';

      if (data.outcome === 'opposing_team_won_previous_hands') {
        title = 'Double Failed!';
        message = `${data.doubleCallerName} (Team ${data.doubleCallerTeam}) called Double but the opposing team has already won hands this ball.\nTeam ${data.winningTeam} has been awarded ${data.ballsAwarded} balls total.`;
        type = 'error';
      } else if (data.outcome === 'success') {
        title = 'Double Success!';
        message = `${data.doubleCallerName} (Team ${data.doubleCallerTeam}) called Double and won the hand!\nTeam ${data.winningTeam} has been awarded ${data.ballsAwarded} balls total.`;
        type = 'success';
      } else if (data.outcome === 'teammate_won') {
        title = 'Double Failed!';
        message = `${data.doubleCallerName} (Team ${data.doubleCallerTeam}) called Double but their teammate ${data.handWinnerName} won the hand!\nTeam ${data.winningTeam} has been awarded ${data.ballsAwarded} balls total.`;
        type = 'error';
      } else {
        title = 'Double Failed!';
        message = `${data.doubleCallerName} (Team ${data.doubleCallerTeam}) called Double but ${data.handWinnerName} (Team ${data.handWinnerTeam}) won the hand!\nTeam ${data.winningTeam} has been awarded ${data.ballsAwarded} balls total.`;
        type = 'error';
      }

      // Update ball scores directly with the values from the server
      // This ensures the client's ball scores match the server's
      updateGameState({
        ballScores: data.ballScores
      });

      // Show notification about the Double outcome
      const notification = {
        title,
        message,
        type,
        duration: 8000
      };

      // Dispatch notification event
      const notificationEvent = new CustomEvent('show-notification', {
        detail: notification
      });
      window.dispatchEvent(notificationEvent);

      // Play appropriate sound
      if (data.outcome === 'success') {
        playSound(SOUNDS.SUCCESS);
      } else {
        playSound(SOUNDS.ERROR);
      }

      // Show the BallResultsDisplay with the appropriate data
      console.log("Setting up BallResultsDisplay with data:", {
        doubleCallerId: data.doubleCallerId,
        doubleCallerTeam: data.doubleCallerTeam,
        handWinnerId: data.handWinnerId,
        handWinnerTeam: data.handWinnerTeam,
        handsWonByOppositeTeam: data.opposingTeamHands?.length || 0,
        outcome: data.outcome
      });

      setBallResultsData({
        doubleCallerId: data.doubleCallerId,
        doubleCallerTeam: data.doubleCallerTeam as 1 | 2,
        handWinnerId: data.handWinnerId,
        handWinnerTeam: data.handWinnerTeam as 1 | 2,
        handsWonByOppositeTeam: data.opposingTeamHands || [],
        lastHand: data.lastHand,
        outcome: data.outcome as any,
        ballsAwarded: data.ballsAwarded,
        winningTeam: data.winningTeam as 1 | 2
      });

      // Make sure any other modals or overlays are hidden
      setIsDealing(false);

      // Show the BallResultsDisplay
      setShowBallResults(true);

      console.log("BallResultsDisplay should now be visible");

      // The BallResultsDisplay will show a Continue Game button after 10 seconds
      // We'll handle the closing in the onContinueGame callback
    };

    // Listen for khanuck called event
    const handleKhanuckCall = (data: {
      playerId: string,
      playerName: string,
      playerTeam: number,
      khanakThreshold?: number,
      teamJordhiPoints?: number,
      opposingTeamJordhiPoints?: number
    }) => {
      console.log(`Khanuck called by ${data.playerName} (Team ${data.playerTeam})`);

      // Show notification that Khanuck has been called
      const notification = {
        title: 'Khanuck Called!',
        message: data.khanakThreshold
          ? `${data.playerName} (Team ${data.playerTeam}) has called Khanuck! Threshold: ${data.khanakThreshold} points`
          : `${data.playerName} (Team ${data.playerTeam}) has called Khanuck!`,
        type: 'info' as const,
        duration: 5000
      };

      // Dispatch notification event
      const notificationEvent = new CustomEvent('show-notification', {
        detail: notification
      });
      window.dispatchEvent(notificationEvent);
    };

    // Listen for khanak outcome event
    const handleKhanakOutcome = (data: {
      success: boolean;
      reason: string;
      ballsAwarded: number;
      winningTeam: number;
      khanakCall: {
        playerId: string;
        playerName: string;
        playerTeam: number;
        opposingTeam: number;
        threshold: number;
        opposingTeamPoints: number;
        opposingTeamHands: any[];
        teamJordhiPoints: number;
        opposingTeamJordhiPoints: number;
      }
    }) => {
      console.log(`Khanak outcome received:`, data);

      // Get the correct team numbers
      const khanakCallerTeam = data.khanakCall.playerTeam as 1 | 2;
      const opposingTeam = data.khanakCall.opposingTeam as 1 | 2;

      console.log(`Khanak caller team: ${khanakCallerTeam}, Opposing team: ${opposingTeam}`);
      console.log(`Team names: Team 1: ${teamNames[1]}, Team 2: ${teamNames[2]}`);

      // Set up the KhanakBallResultsDisplay data
      setKhanakBallResultsData({
        khanakCallerId: data.khanakCall.playerId,
        khanakCallerTeam: khanakCallerTeam,
        opposingTeamHands: data.khanakCall.opposingTeamHands,
        threshold: data.khanakCall.threshold,
        opposingTeamPoints: data.khanakCall.opposingTeamPoints,
        teamJordhiPoints: data.khanakCall.teamJordhiPoints,
        opposingTeamJordhiPoints: data.khanakCall.opposingTeamJordhiPoints,
        outcome: data.reason as 'points_below_threshold' | 'points_above_threshold' | 'last_hand_lost' | 'opposing_team_no_hands' | 'no_jordhi_calls',
        ballsAwarded: data.ballsAwarded,
        winningTeam: data.winningTeam as 1 | 2
      });

      // Show the KhanakBallResultsDisplay
      setShowKhanakBallResults(true);

      // Play appropriate sound based on which team won
      const currentPlayerId = socketService.getSocketId();
      const currentPlayer = players.find(p => p.id === currentPlayerId);

      if (currentPlayer && currentPlayer.team === data.winningTeam) {
        playSound(SOUNDS.SUCCESS);
      } else {
        playSound(SOUNDS.ERROR);
      }
    };

    // Listen for incorrect jordhi 4-ball result
    const handleIncorrectJordhiResult = (data: {
      ballType: string;
      option: string;
      targetPlayerId: string;
      targetPlayerName: string;
      targetPlayerTeam: 1 | 2;
      jordhiValue: number;
      isValidJordhi: boolean;
      jordhiCards?: Array<{ suit: string, value: string }>;
      winningTeam: 1 | 2;
      ballsAwarded: number;
    }) => {
      console.log('Incorrect Jordhi 4-ball result received:', data);

      // Set up the FourBallResultDisplay data
      setFourBallResultData({
        ballType: data.ballType,
        option: data.option,
        targetPlayer: data.targetPlayerName,
        targetTeam: data.targetPlayerTeam,
        isValidJordhi: data.isValidJordhi,
        jordhiValue: data.jordhiValue,
        jordhiCards: data.jordhiCards,
        winningTeam: data.winningTeam,
        ballsAwarded: data.ballsAwarded
      });

      // Show the FourBallResultDisplay
      setShowFourBallResult(true);

      // Play appropriate sound based on which team won
      const currentPlayerId = socketService.getSocketId();
      const currentPlayer = players.find(p => p.id === currentPlayerId);

      if (currentPlayer && currentPlayer.team === data.winningTeam) {
        playSound(SOUNDS.SUCCESS);
      } else {
        playSound(SOUNDS.ERROR);
      }
    };

    // Handle four_ball_awarded event
    const handleFourBallAwarded = (data: {
      ballType: string;
      option: string;
      jordhiCallPlayerId: string;
      jordhiCallerTeam: 1 | 2;
      winningTeam: 1 | 2;
      ballsAwarded: number;
      ballScores: {
        team1: number;
        team2: number;
      }
    }) => {
      console.log('Four ball awarded event received:', data);

      // Update ball scores directly with the values from the server
      // Make sure we're setting the exact values, not adding to existing values
      console.log('Setting ball scores to exactly:', data.ballScores);
      updateGameState({
        ballScores: {
          team1: data.ballScores.team1,
          team2: data.ballScores.team2
        }
      });

      // Find the Jordhi call player's name
      const jordhiCaller = players.find(p => p.id === data.jordhiCallPlayerId);
      const jordhiCallerName = jordhiCaller?.name || 'Unknown player';

      // Find the Jordhi call details from the game store
      const { jordhiCalls } = useGameStore.getState();
      const jordhiCall = jordhiCalls.find(call => call.playerId === data.jordhiCallPlayerId);

      // Set up the FourBallResultDisplay data
      setFourBallResultData({
        ballType: data.ballType,
        option: data.option,
        targetPlayer: jordhiCallerName,
        targetTeam: data.jordhiCallerTeam,
        isValidJordhi: jordhiCall?.isFullyValid || false,
        jordhiValue: jordhiCall?.value || 0,
        jordhiCards: jordhiCall?.jordhiCards,
        winningTeam: data.winningTeam,
        ballsAwarded: data.ballsAwarded
      });

      // Show the FourBallResultDisplay
      setShowFourBallResult(true);

      // Play appropriate sound based on which team won
      const currentPlayerId = socketService.getSocketId();
      const currentPlayer = players.find(p => p.id === currentPlayerId);

      if (currentPlayer && currentPlayer.team === data.winningTeam) {
        playSound(SOUNDS.SUCCESS);
      } else {
        playSound(SOUNDS.ERROR);
      }
    };

    socketService.on("game_phase_updated", handleGamePhaseUpdated);
    socketService.on("shuffle_animation", handleShuffleAnimation);
    socketService.on("shuffle_complete", handleShuffleComplete);
    socketService.on("cut_requested", handleCutRequested);
    socketService.on("cut_complete", handleCutComplete);
    socketService.on("trump_selector_assigned", handleTrumpSelectorAssigned);
    socketService.on("first_four_dealt", handleFirstFourDealt);
    socketService.on("bidding_complete", handleBiddingComplete);
    socketService.on("trump_selected", handleTrumpSelected);
    socketService.on("first_player_determined", handleFirstPlayerDetermined);
    socketService.on("deal_final_cards_request", handleDealFinalCardsRequest);
    socketService.on("final_cards_dealt", handleFinalCardsDealt);
    socketService.on("cards_dealt", handleCardsDealt);
    socketService.on("game_held", handleGameHeld);
    socketService.on("game_hold_complete", handleGameHoldComplete);
    socketService.on("thunee_called", handleThuneeCall);
    socketService.on("royal_thunee_called", handleRoyalThuneeCall);
    socketService.on("thunee_success", handleThuneeSuccess);
    socketService.on("thunee_failure", handleThuneeFailure);
    socketService.on("double_called", handleDoubleCall);
    socketService.on("double_outcome", handleDoubleOutcome);
    socketService.on("khanuck_called", handleKhanuckCall);
    socketService.on("khanak_outcome", handleKhanakOutcome);
    socketService.on("incorrect_jordhi_result", handleIncorrectJordhiResult);
    socketService.on("four_ball_awarded", handleFourBallAwarded);

    // Handle never_follow_suit_result event
    const handleNeverFollowSuitResult = (data: {
      ballType: string;
      option: string;
      targetPlayer: string;
      targetPlayerName: string;
      targetTeam: 1 | 2;
      accuserId: string;
      accuserName: string;
      accuserTeam: 1 | 2;
      handNumber: number;
      leadSuit: string;
      cardPlayed: {
        suit: string;
        value: string;
      };
      handBeforePlaying: Array<{
        suit: string;
        value: string;
      }>;
      isValid: boolean;
      winningTeam: 1 | 2;
      ballsAwarded: number;
      ballScores: {
        team1: number;
        team2: number;
      }
    }) => {
      console.log('Never follow suit result event received:', data);

      // Update ball scores directly with the values from the server
      // Make sure we're setting the exact values, not adding to existing values
      console.log('Setting ball scores to exactly:', data.ballScores);
      updateGameState({
        ballScores: {
          team1: data.ballScores.team1,
          team2: data.ballScores.team2
        }
      });

      // Set the result data and show the result display
      setNeverFollowSuitResultData(data);
      setShowNeverFollowSuitResult(true);

      // Play a sound for the notification
      playSound(SOUNDS.SUCCESS, 0.7);
    };

    socketService.on("never_follow_suit_result", handleNeverFollowSuitResult);

    // Handle under_chopped_result event
    const handleUnderChoppedResult = (data: {
      ballType: string;
      option: string;
      targetPlayer: string;
      targetPlayerName: string;
      targetTeam: 1 | 2;
      accuserId: string;
      accuserName: string;
      accuserTeam: 1 | 2;
      handNumber: number;
      trumpSuit: string;
      leadSuit?: string;
      cardPlayed: {
        suit: string;
        value: string;
      };
      handBeforePlaying: Array<{
        suit: string;
        value: string;
      }>;
      selectedHandCards?: Array<{
        suit: string;
        value: string;
        playedBy: string;
        playerName: string;
        playerTeam: 1 | 2;
        playOrder?: number;
      }>;
      isValid: boolean;
      isPlayedCardTrump?: boolean;
      hasHigherTrump?: boolean;
      trumpCardsCount?: number;
      hadOnlyTrumps?: boolean;
      hadLeadSuitCards?: boolean;
      isHighestTrumpPlayed?: boolean;
      invalidReason?: string;
      winningTeam: 1 | 2;
      ballsAwarded: number;
      ballScores: {
        team1: number;
        team2: number;
      };
      customTrumpRanking: string[];
    }) => {
      console.log('Under chopped result event received:', data);
      console.log('Selected hand cards:', data.selectedHandCards);
      console.log('Player hand before playing:', data.handBeforePlaying);
      console.log('Invalid reason:', data.invalidReason);

      // Update ball scores directly with the values from the server
      console.log('Setting ball scores to exactly:', data.ballScores);
      updateGameState({
        ballScores: {
          team1: data.ballScores.team1,
          team2: data.ballScores.team2
        }
      });

      // Update our local state for the UnderChoppedResultDisplay component
      setUnderChoppedResult(data);
      setUnderChoppedResultOpen(true);

      // Play a sound for the notification based on whether the current player's team won
      const currentPlayerId = socketService.getSocketId();
      const currentPlayer = players.find(p => p.id === currentPlayerId);

      if (currentPlayer && currentPlayer.team === data.winningTeam) {
        playSound(SOUNDS.SUCCESS, 0.7);
      } else {
        playSound(SOUNDS.ERROR, 0.7);
      }
    };

    socketService.on("under_chopped_result", handleUnderChoppedResult);

    // Listen for valid Jordhi call event to show the reveal option
    socketService.on("jordhi_called", (data: {
      playerId: string;
      playerName: string;
      playerTeam: 1 | 2;
      value: number;
      isValidCards: boolean;
      isValidHandCount: boolean;
      isFullyValid: boolean;
      jordhiSuit: string;
      jordhiCards?: Array<{ suit: string, value: string }>;
    }) => {
      console.log("Jordhi called by current player:", data);

      // Only show the reveal option for valid Jordhi calls (valid cards)
      // This is the player who made the call
      if (data.isValidCards && data.jordhiCards && data.jordhiCards.length > 0) {
        setJordhiRevealData({
          playerId: data.playerId,
          playerName: data.playerName,
          playerTeam: data.playerTeam,
          value: data.value,
          jordhiSuit: data.jordhiSuit,
          jordhiCards: data.jordhiCards
        });
        setShowJordhiRevealModal(true);
      }
    });

    // Listen for Jordhi cards revealed event
    socketService.on("jordhi_cards_revealed", (data: {
      playerName: string;
      playerTeam: 1 | 2;
      value: number;
      jordhiSuit: string;
      cardsRevealed: boolean;
      jordhiCards?: Array<{ suit: string, value: string }>;
    }) => {
      console.log("Jordhi notification received:", data);

      // Show the Jordhi notification to all players
      setJordhiCardsRevealData(data);
      setShowJordhiCardsReveal(true);

      // Play a sound for the notification
      playSound(SOUNDS.JORDHI_CALL, 0.7);

      // Update the Jordhi call in the gameStore to mark whether cards were revealed
      const { jordhiCalls, updateGameState } = useGameStore.getState();
      const updatedJordhiCalls = jordhiCalls.map(call => {
        // Find the matching Jordhi call
        if (call.playerName === data.playerName &&
            call.playerTeam === data.playerTeam &&
            call.value === data.value &&
            call.jordhiSuit === data.jordhiSuit) {
          // Update the cardsRevealed flag
          return {
            ...call,
            cardsRevealed: data.cardsRevealed
          };
        }
        return call;
      });

      // Update the gameStore
      updateGameState({ jordhiCalls: updatedJordhiCalls });
    });

    // Listen for ball_completed event
    socketService.on("ball_completed", (data: {
      ballId: number,
      winner: 1 | 2,
      points: {
        team1: number,
        team2: number
      },
      nextDealer: string,
      ballScores: {
        team1: number,
        team2: number
      },
      doubleProcessed?: boolean,
      thuneeSuccess?: boolean,
      thuneeFailure?: boolean,
      khanakProcessed?: boolean,
      fourBallAwarded?: boolean,
      fourBallOption?: string,
      fourBallWinningTeam?: 1 | 2,
      timeout?: boolean,
      timedOutPlayer?: {
        id: string,
        name: string,
        team: 1 | 2
      },
      timeoutMessage?: string,
      khanakResult?: {
        playerId: string,
        playerName: string,
        playerTeam: 1 | 2,
        opposingTeam: 1 | 2,
        threshold: number,
        opposingTeamPoints: number,
        opposingTeamHands: any[],
        teamJordhiPoints: number,
        opposingTeamJordhiPoints: number,
        reason: string,
        ballsAwarded: number,
        winningTeam: 1 | 2
      },
      handWinnerId?: string,
      handWinnerTeam?: 1 | 2,
      ballsAwarded?: number,
      handsWonByNonTrumpingTeam?: any[],
      targetScore?: number,
      initialTargetScore?: number,
      jordhiAdjustments?: {
        nonTrumpingTeam: number,
        trumpingTeam: number
      },
      finalHandAdjustment?: number,
      nonTrumpingTeamPoints?: number,
      trumpingTeam?: 1 | 2,
      isCallAndLost?: boolean
    }) => {
      console.log('Ball completed event received:', data);

      // If this is a timeout, show the TimeoutBallResultsDisplay
      if (data.timeout && data.timedOutPlayer) {
        console.log('Ball completed due to timeout, showing TimeoutBallResultsDisplay');

        // Update ball scores directly with the values from the server
        updateGameState({
          ballScores: data.ballScores
        });

        // Set up the timeout ball results data
        setTimeoutBallResultsData({
          timedOutPlayer: data.timedOutPlayer,
          opposingTeam: data.timedOutPlayer.team === 1 ? 2 : 1,
          ballsAwarded: data.ballsAwarded || 1
        });

        // Show the TimeoutBallResultsDisplay
        setShowTimeoutBallResults(true);

        // Play error sound
        playSound(SOUNDS.ERROR);

        return;
      }

      // If this is after a Double call, we'll handle it in the BallResultsDisplay component
      // with the Continue Game button, so we don't need to do anything here
      if (data.doubleProcessed) {
        console.log('Double was processed for this ball, waiting for user to click Continue Game');
        return;
      }

      // If this is after a Thunee success or failure, we don't need to show the standard ball results
      if (data.thuneeSuccess || data.thuneeFailure) {
        console.log('Thunee was processed for this ball, no need to show standard ball results');
        return;
      }

      // If this is after a 4-ball award for incorrect Jordhi, we don't need to show the standard ball results
      // because we're already showing the FourBallResultDisplay
      if (data.fourBallAwarded) {
        console.log('4-ball was awarded for this ball, no need to show standard ball results');
        return;
      }

      // If this is after a Khanak call, show the KhanakBallResultsDisplay
      if (data.khanakProcessed) {
        console.log('Khanak was processed for this ball, showing KhanakBallResultsDisplay');

        // Update ball scores directly with the values from the server
        updateGameState({
          ballScores: data.ballScores
        });

        // Set up the KhanakBallResultsDisplay data
        const khanakCallerTeam = data.khanakResult?.playerTeam as 1 | 2;
        const opposingTeam = khanakCallerTeam === 1 ? 2 : 1;

        console.log(`Khanak caller team: ${khanakCallerTeam}, Opposing team: ${opposingTeam}`);
        console.log(`Team names: Team 1: ${teamNames[1]}, Team 2: ${teamNames[2]}`);

        setKhanakBallResultsData({
          khanakCallerId: data.khanakResult?.playerId,
          khanakCallerTeam: khanakCallerTeam,
          handWinnerId: data.handWinnerId,
          handWinnerTeam: data.handWinnerTeam as 1 | 2,
          opposingTeamHands: data.khanakResult?.opposingTeamHands || [],
          threshold: data.khanakResult?.threshold,
          opposingTeamPoints: data.khanakResult?.opposingTeamPoints,
          teamJordhiPoints: data.khanakResult?.teamJordhiPoints,
          opposingTeamJordhiPoints: data.khanakResult?.opposingTeamJordhiPoints,
          outcome: data.khanakResult?.reason as 'points_below_threshold' | 'points_above_threshold' | 'last_hand_lost' | 'opposing_team_no_hands' | 'no_jordhi_calls',
          ballsAwarded: data.khanakResult?.ballsAwarded,
          winningTeam: data.khanakResult?.winningTeam as 1 | 2
        });

        // Show the KhanakBallResultsDisplay
        setShowKhanakBallResults(true);

        // Play appropriate sound based on which team won
        const currentPlayerId = socketService.getSocketId();
        const currentPlayer = players.find(p => p.id === currentPlayerId);

        if (currentPlayer && currentPlayer.team === data.khanakResult?.winningTeam) {
          playSound(SOUNDS.SUCCESS);
        } else {
          playSound(SOUNDS.ERROR);
        }

        return;
      }

      // For standard ball completions (no Double, Thunee, Khanak, or timeout), show the StandardBallResultsDisplay
      if (!data.doubleProcessed && !data.thuneeSuccess && !data.thuneeFailure && !data.khanakProcessed && !data.timeout) {
        console.log('Standard ball completion, showing StandardBallResultsDisplay');

        // Update ball scores directly with the values from the server
        updateGameState({
          ballScores: data.ballScores
        });

        // Verify the winner is correct based on the game rules
        // Make sure we get the trumping team from the server data
        let trumpingTeam = data.trumpingTeam !== undefined ? data.trumpingTeam : 1;
        console.log(`App.tsx: Received trumpingTeam=${trumpingTeam} from server`);

        // Ensure we have a valid trumping team (either 1 or 2)
        if (trumpingTeam !== 1 && trumpingTeam !== 2) {
          console.error(`Invalid trumping team: ${trumpingTeam}, defaulting to team 1`);
          // Default to team 1 as the trumping team if we don't have a valid value
          trumpingTeam = 1;
        }

        // Calculate the nonTrumpingTeam based on the trumpingTeam
        // TypeScript doesn't know about the nonTrumpingTeam property yet
        const nonTrumpingTeam = trumpingTeam === 1 ? 2 : 1;
        console.log(`App.tsx: Using nonTrumpingTeam=${nonTrumpingTeam} (calculated from trumpingTeam)`);

        // Check if the server sent a nonTrumpingTeam property (for future use)
        if ((data as any).nonTrumpingTeam !== undefined) {
          console.log(`Server sent nonTrumpingTeam=${(data as any).nonTrumpingTeam}, using calculated value for now`);
        }

        // Log all the data received from the server for debugging
        console.log("Ball completion data received:", data);
        const handsWonByNonTrumpingTeam = data.handsWonByNonTrumpingTeam || [];

        // Calculate the actual points from the hands won
        const calculatedPoints = handsWonByNonTrumpingTeam.reduce((sum, hand) => sum + (hand.points || 0), 0);
        const targetScore = data.targetScore || 105;

        // In Thunee, if the non-trumping team reaches their target score, they win
        // Otherwise, the trumping team wins
        const correctWinner = calculatedPoints >= targetScore ? nonTrumpingTeam : trumpingTeam;

        if (data.winner !== correctWinner) {
          console.error(`Ball winner mismatch! Server sent ${data.winner}, but calculated ${correctWinner}`);
          console.error(`Non-trumping team: ${nonTrumpingTeam}, Calculated Points: ${calculatedPoints}, Target: ${targetScore}`);
        }

        // Log the points for debugging
        console.log(`Calculated points: ${calculatedPoints}, Server points: ${data.nonTrumpingTeamPoints || 0}`);
        if (calculatedPoints !== (data.nonTrumpingTeamPoints || 0)) {
          console.error(`Points mismatch! Calculated ${calculatedPoints} but server sent ${data.nonTrumpingTeamPoints || 0}`);
        }

        // Set up the StandardBallResultsDisplay data
        setStandardBallResultsData({
          handsWonByNonTrumpingTeam: data.handsWonByNonTrumpingTeam || [],
          targetScore: targetScore,
          initialTargetScore: data.initialTargetScore || 105,
          jordhiAdjustments: data.jordhiAdjustments || {
            nonTrumpingTeam: 0,
            trumpingTeam: 0
          },
          finalHandAdjustment: data.finalHandAdjustment || 0,
          nonTrumpingTeamPoints: calculatedPoints,
          trumpingTeam: data.trumpingTeam || 1,
          winningTeam: correctWinner, // Use the correct winner based on our calculation
          ballsAwarded: data.ballsAwarded || 1,
          isCallAndLost: data.isCallAndLost || false // Include whether this was a "Call and lost" scenario
        });

        // Show the StandardBallResultsDisplay
        setShowStandardBallResults(true);

        // Play appropriate sound based on which team won
        const currentPlayerId = socketService.getSocketId();
        const currentPlayer = players.find(p => p.id === currentPlayerId);

        if (currentPlayer && currentPlayer.team === data.winner) {
          playSound(SOUNDS.SUCCESS);
        } else {
          playSound(SOUNDS.ERROR);
        }
      }
    });
    socketService.on("thunee_opportunity", (data: {
      playerId: string,
      playerName: string,
      stage: "trumper" | "first-remaining" | "last-remaining",
      duration: number
    }) => {
      console.log(`Thunee opportunity for ${data.playerName} (${data.playerId}) - Stage: ${data.stage}`);

      // Check if this is for the current player
      const currentPlayerId = socketService.getSocketId();
      if (data.playerId === currentPlayerId) {
        // Set up the Thunee calling prompt
        setThuneeCallingStage(data.stage);
        setThuneeCallingDuration(data.duration);
        setShowThuneePrompt(true);
      } else {
        // For other players, hide the Hold Game button during this time
        setShowHoldGameButton(false);
      }
    });

    // Listen for trumper opportunity complete event
    socketService.on("trumper_opportunity_complete", (data: {
      playerId: string,
      playerName: string,
      nextStage: "first-remaining" | "last-remaining"
    }) => {
      console.log(`Trumper ${data.playerName} opportunity complete, next stage: ${data.nextStage}`);

      // Check if this player is not the Trumper
      const isTrumper = useGameStore.getState().isTrumpSelector;
      const currentPlayerId = socketService.getSocketId();

      if (!isTrumper && currentPlayerId !== data.playerId) {
        // Show the Hold Game button to non-Trumper players
        console.log("This player is not the Trumper, showing Hold Game button");
        setShowHoldGameButton(true);
        setThuneeCallingStage(data.nextStage);
        setThuneeCallingDuration(data.nextStage === "first-remaining" ? 3 : 2);
      }
    });

    return () => {
      socketService.off("game_phase_updated", handleGamePhaseUpdated);
      socketService.off("shuffle_animation", handleShuffleAnimation);
      socketService.off("shuffle_complete", handleShuffleComplete);
      socketService.off("cut_requested", handleCutRequested);
      socketService.off("cut_complete", handleCutComplete);
      socketService.off("trump_selector_assigned", handleTrumpSelectorAssigned);
      socketService.off("first_four_dealt", handleFirstFourDealt);
      socketService.off("bidding_complete", handleBiddingComplete);
      socketService.off("trump_selected", handleTrumpSelected);
      socketService.off("first_player_determined", handleFirstPlayerDetermined);
      socketService.off("deal_final_cards_request", handleDealFinalCardsRequest);
      socketService.off("final_cards_dealt", handleFinalCardsDealt);
      socketService.off("cards_dealt", handleCardsDealt);
      socketService.off("game_held", handleGameHeld);
      socketService.off("game_hold_complete", handleGameHoldComplete);
      socketService.off("thunee_called", handleThuneeCall);
      socketService.off("royal_thunee_called", handleRoyalThuneeCall);
      socketService.off("thunee_success", handleThuneeSuccess);
      socketService.off("thunee_failure", handleThuneeFailure);
      socketService.off("double_called", handleDoubleCall);
      socketService.off("double_outcome", handleDoubleOutcome);
      socketService.off("khanuck_called", handleKhanuckCall);
      socketService.off("khanak_outcome", handleKhanakOutcome);
      socketService.off("incorrect_jordhi_result", handleIncorrectJordhiResult);
      socketService.off("four_ball_awarded", handleFourBallAwarded);
      socketService.off("never_follow_suit_result", handleNeverFollowSuitResult);
      socketService.off("under_chopped_result", handleUnderChoppedResult);
      socketService.off("ball_completed");
      socketService.off("thunee_opportunity");
      socketService.off("trumper_opportunity_complete");
      socketService.off("jordhi_called");
      socketService.off("jordhi_cards_revealed");

      // Reset animation states
      setShowShuffleAnimation(false);
      setShowCutAnimation(false);
      setShowDealerAnimation(false);
      setShowPlayerAnimation(false);
      // Deal four animation removed as requested
      // setShowDealFourAnimation(false);
    };
  }, []);

  // Handle connection state
  useEffect(() => {
    // Only redirect if we've been disconnected for more than 5 seconds
    // This gives time for automatic reconnection to work
    let disconnectionTimer: NodeJS.Timeout | null = null;

    if (!isConnected && lobbyCode) {
      // Set a timer to redirect after 5 seconds if still disconnected
      disconnectionTimer = setTimeout(() => {
        // Don't redirect if in spectator mode
        if (!isSpectator) {
          console.log("Disconnected for too long, redirecting to home");
          navigate("/");
        } else {
          console.log("Disconnected for too long, but in spectator mode - not redirecting");
          // Just show an error message for spectators
          setErrorMessage("Connection lost. Attempting to reconnect as spectator...");
        }
      }, 5000);

      // Show a temporary error message
      setErrorMessage("Connection lost. Attempting to reconnect...");
    } else if (!lobbyCode && !isSpectator && !gameCode) {
      // If no lobby code, not in spectator mode, and no game code, redirect immediately
      console.log("No lobby code, not in spectator mode, and no game code - redirecting to home");
      navigate("/");
    } else if (isConnected && disconnectionTimer) {
      // If we reconnected, clear the timer and error
      clearTimeout(disconnectionTimer);
      setErrorMessage(null);
    }

    return () => {
      // Clean up timer if component unmounts
      if (disconnectionTimer) {
        clearTimeout(disconnectionTimer);
      }
    };
  }, [isConnected, lobbyCode, navigate]);

  // Initialize game state with lobby players
  useEffect(() => {
    if (lobbyPlayers.length > 0 && players.length === 0) {
      // Convert lobby players to game players
      const gamePlayers = lobbyPlayers.map((player) => ({
        ...player,
        team: (player.team as 1 | 2) || 1, // Default to team 1 if not set
        isDealer: false,
        isTrumpSelector: false,
        isCurrentTurn: false,
      }));
      updateGameState({ players: gamePlayers });

      // Log the players for debugging
      console.log("Game initialized with players:", gamePlayers);
    }
  }, [lobbyPlayers, players, updateGameState]);

  // Show error from the game store
  useEffect(() => {
    if (gameError) {
      setErrorMessage(gameError);
      // Clear error after 5 seconds
      const timer = setTimeout(() => setErrorMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [gameError]);



  // Handle rules completion
  const handleRulesComplete = () => {
    setShowRules(false);
    setGamePhase("play-timeframe-voting");
  };

  return (
    <div className="bg-greenbg h-screen w-screen overflow-hidden">
      {/* Orientation Warning for mobile devices */}
      <OrientationWarning />

      {/* Spectator View - Only render this if isSpectator is true */}
      {isSpectator ? (
        <SpectatorView />
      ) : (
        <>
          {/* Game Rules Modal */}
          {gamePhase === "rules" && (
            <GameRules
              onClose={() => setShowRules(false)}
              onContinue={handleRulesComplete}
            />
          )}

          {/* Play Timeframe Voting Phase */}
          {gamePhase === "play-timeframe-voting" && (
            <PlayTimeframeVoting
              onVotingComplete={() => setGamePhase("dealer-determination")}
            />
          )}

          {/* Dealer Determination Phase */}
          {gamePhase === "dealer-determination" && (
            <DealerDetermination
              players={players}
              teamNames={teamNames}
              onDealerSelected={() => setGamePhase("shuffle")}
            />
          )}

          {/* Shuffle Phase - Only the dealer sees this */}
          {gamePhase === "shuffle" && isDealer && (
            <ShuffleButton
              onShuffleComplete={() => setGamePhase("cut")}
            />
          )}

          {/* Shuffle Phase Message - Non-dealers see this */}
          {gamePhase === "shuffle" && !isDealer && (
            <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
              <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full text-center">
                <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Waiting for Dealer</h2>
                <p className="text-white mb-6">The dealer is shuffling the deck...</p>
              </div>
            </div>
          )}

          {/* Cut Phase - Only the dealer sees this */}
          {gamePhase === "cut" && isDealer && (
            <CutButton
              onCutComplete={() => setGamePhase("deal-first-four")}
            />
          )}

          {/* Cut Phase Message - Non-dealers see this unless they're the one cutting */}
          {gamePhase === "cut" && !isDealer && !showCutOptions && (
            <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
              <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full text-center">
                <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Waiting for Cut</h2>
                <p className="text-white mb-6">The dealer is giving someone the option to cut the deck...</p>
              </div>
            </div>
          )}

          {/* Cut Options for player who needs to cut */}
          {showCutOptions && (
            <CutOptions
              isVisible={showCutOptions}
              onCutComplete={() => setShowCutOptions(false)}
            />
          )}

          {/* Bidding Modal for bidding phase */}
          {gamePhase === "bidding" && (
            <BiddingModal
              isVisible={true}
              onBiddingComplete={() => {
                // The bidding_complete event will handle the transition to trump selection
              }}
            />
          )}

          {/* Trump Selector for player who needs to select trump */}
          {showTrumpSelector && (
            <TrumpSelector
              isVisible={showTrumpSelector}
              onTrumpSelected={() => setShowTrumpSelector(false)}
            />
          )}

          {/* This section is no longer needed since we're showing the BiddingModal to all players */}

          {/* Select Trump Phase - Non-trump selectors see this */}
          {gamePhase === "select-trump" && !isTrumpSelector && !showTrumpSelector && (
            <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
              <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full text-center">
                <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Waiting for Trump Selection</h2>
                <p className="text-white mb-6">The highest bidder is selecting the trump suit...</p>
              </div>
            </div>
          )}

          {/* Deal First Four Phase - Only the dealer sees this */}
          {gamePhase === "deal-first-four" && isDealer && (
            <>
              {/* <div className="fixed top-4 left-4 bg-black/70 text-white p-2 rounded text-xs z-50">
                Dealer Status: {isDealer ? "You are the dealer" : "You are not the dealer"}
              </div> */}
              <DealFirstFour
                onDealComplete={() => {
                  setIsDealing(true);
                }}
                onDealStart={handleDealStart}
              />
            </>
          )}

          {/* Deal Phase Message - Non-dealers see this */}
          {gamePhase === "deal-first-four" && !isDealer && (
            <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
              <div className="fixed top-4 left-4 bg-black/70 text-white p-2 rounded text-xs z-50">
                Dealer Status: {isDealer ? "You are the dealer" : "You are not the dealer"}
              </div>
              <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full text-center">
                <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Waiting for Dealer</h2>
                <p className="text-white mb-6">The dealer is dealing the cards...</p>
              </div>
            </div>
          )}

          {/* Review Cards Phase - All players see this */}
          {gamePhase === "review-cards" && (
            <div className="fixed inset-0 bg-black/80 flex flex-col items-center justify-center z-50">
              <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full text-center mb-8">
                <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Review Your Cards</h2>
                <p className="text-white mb-6">Take a moment to look at your cards before trump selection begins...</p>
                <div className="w-16 h-16 border-4 border-[#E1C760] border-t-transparent rounded-full animate-spin mx-auto"></div>
              </div>

              {/* Show player's cards during review phase */}
              <div className="w-full max-w-4xl px-4 ">
                <div className="min-w-max">
                  <PlayerHand cards={initialHand.length > 0 ? initialHand : hand} />
                </div>
              </div>
            </div>
          )}

          {/* Waiting for Final Cards Phase - Only the dealer sees the button */}
          {gamePhase === "waiting-for-final-cards" && isDealer && (
            <div className="fixed inset-0 bg-black/80 flex flex-col items-center justify-center z-50">
              <div className="bg-black border-2 border-[#E1C760] rounded-lg max-w-md w-full text-center ">
                <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Deal Final Cards</h2>
                <p className="text-white mb-6">As the dealer, you need to deal the final 2 cards to each player.</p>
              </div>

              <DealFinalCards
                onDealComplete={() => {
                  setGamePhase("gameplay");
                }}
                onDealStart={handleFinalDealStart}
              />

              {/* Show player's cards during waiting phase */}
              <div className="w-full max-w-4xl px-4 mt-8 ">
                <div className="min-w-max">
                  <PlayerHand cards={initialHand.length > 0 ? initialHand : hand} />
                </div>
              </div>
            </div>
          )}

          {/* Waiting for Final Cards Phase - Non-dealers see this */}
          {gamePhase === "waiting-for-final-cards" && !isDealer && (
            <div className="fixed inset-0 bg-black/80 flex flex-col items-center justify-center z-50">
              <div className="bg-black border-2 border-[#E1C760] rounded-lg p-2 max-w-md w-full text-center mb-8">
                <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Waiting for Dealer</h2>
                <p className="text-white mb-6">The dealer is dealing the final cards...</p>
              </div>

              {/* Show player's cards during waiting phase */}
              <div className="w-full max-w-4xl px-4 ">
                <div className="min-w-max">
                  <PlayerHand cards={initialHand.length > 0 ? initialHand : hand} />
                </div>
              </div>
            </div>
          )}

          {/* Dealing in progress indicator */}
          {isDealing && (
            <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
              <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full text-center">
                <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Dealing Cards</h2>
                <p className="text-white mb-6">Cards are being dealt to all players...</p>
                <div className="w-16 h-16 border-4 border-[#E1C760] border-t-transparent rounded-full animate-spin mx-auto"></div>
              </div>
            </div>
          )}

          {/* Add BurgerMenu */}
          <BurgerMenu />

          {/* Notification component for ball completion and other events */}
          <Notification />

          {/* Jordhi Notification Manager */}
          <JordhiNotificationManager />

          {/* Jordhi Reveal Modal - shown to player who made a valid Jordhi call */}
          {showJordhiRevealModal && jordhiRevealData && (
            <JordhiRevealModal
              isOpen={showJordhiRevealModal}
              onClose={() => setShowJordhiRevealModal(false)}
              jordhiCall={jordhiRevealData}
            />
          )}

          {/* Jordhi Cards Reveal - shown to all players when someone reveals their Jordhi cards */}
          {showJordhiCardsReveal && jordhiCardsRevealData && (
            <JordhiCardsReveal
              isOpen={showJordhiCardsReveal}
              onClose={() => setShowJordhiCardsReveal(false)}
              revealData={jordhiCardsRevealData}
            />
          )}

          {/* Ball Results Display for Double calls - Highest z-index to ensure it's visible */}
          {showBallResults && (
            <div className="fixed inset-0 z-[9999] flex items-center justify-center">
              <BallResultsDisplay
                isVisible={true}
                onClose={() => {
                  console.log("BallResultsDisplay closed");
                  setShowBallResults(false);
                }}
                doubleCallerId={ballResultsData.doubleCallerId}
                doubleCallerTeam={ballResultsData.doubleCallerTeam}
                handWinnerId={ballResultsData.handWinnerId}
                handWinnerTeam={ballResultsData.handWinnerTeam}
                handsWonByOppositeTeam={ballResultsData.handsWonByOppositeTeam}
                lastHand={ballResultsData.lastHand}
                outcome={ballResultsData.outcome}
                ballsAwarded={ballResultsData.ballsAwarded}
                winningTeam={ballResultsData.winningTeam}
                displayDuration={10000}
                onContinueGame={() => {
                  // Handle the continue game action
                  // This will be called when the user clicks the Continue Game button
                  console.log("Continue Game button clicked");
                  setShowBallResults(false);
                  setShowContinueGameButton(false);

                  // The game phase should already be updated to "shuffle" by the server
                  // after the ball is completed, so we don't need to do anything else here
                }}
              />
            </div>
          )}

          {/* Khanak Ball Results Display - Highest z-index to ensure it's visible */}
          {showKhanakBallResults && (
            <div className="fixed inset-0 z-[9999] flex items-center justify-center">
              <KhanakBallResultsDisplay
                isVisible={true}
                onClose={() => {
                  console.log("KhanakBallResultsDisplay closed");
                  setShowKhanakBallResults(false);
                }}
                khanakCallerId={khanakBallResultsData.khanakCallerId}
                khanakCallerTeam={khanakBallResultsData.khanakCallerTeam}
                handWinnerId={khanakBallResultsData.handWinnerId}
                handWinnerTeam={khanakBallResultsData.handWinnerTeam}
                opposingTeamHands={khanakBallResultsData.opposingTeamHands}
                threshold={khanakBallResultsData.threshold}
                opposingTeamPoints={khanakBallResultsData.opposingTeamPoints}
                teamJordhiPoints={khanakBallResultsData.teamJordhiPoints}
                opposingTeamJordhiPoints={khanakBallResultsData.opposingTeamJordhiPoints}
                outcome={khanakBallResultsData.outcome}
                ballsAwarded={khanakBallResultsData.ballsAwarded}
                winningTeam={khanakBallResultsData.winningTeam}
                displayDuration={15000}
                onContinueGame={() => {
                  // Handle the continue game action
                  console.log("Continue Game button clicked");
                  setShowKhanakBallResults(false);

                  // The game phase should already be updated to "shuffle" by the server
                  // after the ball is completed, so we don't need to do anything else here
                }}
              />
            </div>
          )}

          {/* Standard Ball Results Display for normal ball completion - Highest z-index to ensure it's visible */}
          {showStandardBallResults && (
            <div className="fixed inset-0 z-[9999] flex items-center justify-center">
              <StandardBallResultsDisplay
                isVisible={true}
                onClose={() => {
                  console.log("StandardBallResultsDisplay closed");
                  setShowStandardBallResults(false);
                }}
                handsWonByNonTrumpingTeam={standardBallResultsData.handsWonByNonTrumpingTeam}
                targetScore={standardBallResultsData.targetScore}
                initialTargetScore={standardBallResultsData.initialTargetScore}
                jordhiAdjustments={standardBallResultsData.jordhiAdjustments}
                finalHandAdjustment={standardBallResultsData.finalHandAdjustment}
                nonTrumpingTeamPoints={standardBallResultsData.nonTrumpingTeamPoints}
                trumpingTeam={standardBallResultsData.trumpingTeam}
                winningTeam={standardBallResultsData.winningTeam}
                ballsAwarded={standardBallResultsData.ballsAwarded}
                isCallAndLost={standardBallResultsData.isCallAndLost}
                displayDuration={10000}
                onContinueGame={() => {
                  // Handle the continue game action
                  // This will be called when the user clicks the Continue Game button
                  console.log("Continue Game button clicked");
                  setShowStandardBallResults(false);

                  // The game phase should already be updated to "shuffle" by the server
                  // after the ball is completed, so we don't need to do anything else here
                }}
              />
            </div>
          )}

          {/* Timeout Ball Results Display - Highest z-index to ensure it's visible */}
          {showTimeoutBallResults && (
            <div className="fixed inset-0 z-[9999] flex items-center justify-center">
              <TimeoutBallResultsDisplay
                isVisible={true}
                onClose={() => {
                  console.log("TimeoutBallResultsDisplay closed");
                  setShowTimeoutBallResults(false);
                }}
                timedOutPlayer={timeoutBallResultsData.timedOutPlayer}
                opposingTeam={timeoutBallResultsData.opposingTeam}
                ballsAwarded={timeoutBallResultsData.ballsAwarded}
                displayDuration={3000} // 3 seconds as requested
                onContinueGame={() => {
                  // Handle the continue game action
                  console.log("Continue Game button clicked");
                  setShowTimeoutBallResults(false);

                  // The game phase should already be updated to "shuffle" by the server
                  // after the ball is completed, so we don't need to do anything else here
                }}
              />
            </div>
          )}

          {/* Chat component */}
          <Chat />

          {/* Chat Button - positioned in top right corner */}
          <ChatButton />

          {/* Video Call component */}
          <VideoCall />

          {/* Error message */}
          {errorMessage && (
            <div className="absolute top-20 left-0 right-0 z-50 mx-auto w-11/12 max-w-md">
              <Alert
                variant="destructive"
                className="bg-red-900/50 border border-red-500"
              >
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  {errorMessage}
                </AlertDescription>
              </Alert>
            </div>
          )}

          {/* Game content - only show when in gameplay phase */}
          {gamePhase === "gameplay" && (
            <>
          {/* Hold Game Button - Show only when the game has just started and before any cards are played */}
          {showHoldGameButton && (
            <HoldGameButton
              playerRole={thuneeCallingStage || (isTrumpSelector ? "trumper" : "remaining-player")}
              onHoldComplete={() => {
                console.log("Hold Game button auto-hide complete");
                setShowHoldGameButton(false);
                setGameHeld(false);
                // The Call button will be disabled when gameHeld is set to false
              }}
            />
          )}

          {/* Thunee Calling Prompt - Show when a player needs to decide whether to call Thunee */}
          {showThuneePrompt && thuneeCallingStage && (
            <ThuneeCallingPrompt
              key={`thunee-prompt-${thuneeCallingStage}-${Date.now()}`} // Force re-render with unique key
              playerRole={thuneeCallingStage}
              duration={thuneeCallingDuration}
              onComplete={() => {
                console.log("Thunee calling prompt complete");
                // Immediately hide the prompt
                setShowThuneePrompt(false);
              }}
            />
          )}

          {/* Card Dealing Animation */}
          {showCardDealing && (
            <CardDealing
              isVisible={true}
              onDealingComplete={() => setShowCardDealing(false)}
            />
          )}

          {/* Card Passing Status */}
          <CardPassingStatus />

          {/* Card Passing Modal */}
          <CardPassingModal />

          {/* Dealer Indicator */}
          <DealerIndicator />

          {/* Player Positions */}
          {/* <PlayerPositions /> */}

          {/* Turn Indicator */}
          <TurnIndicator />

          {/* Trump Display */}
          {/* <TrumpDisplay /> */}

          {/* Target Scores Display */}
          {/* <div className="absolute top-[140px] right-4 z-40">
            <TargetScores />
          </div> */}

          {/* Ball Scores Cards Display */}

          {/* Game Status */}
          {/* <GameStatus /> */}

          {/* Debug Controls */}
          {/* {process.env.NODE_ENV === 'development' && <DebugControls />} */}

          {/* Played Cards Arrangement */}
          <PlayedCardsArrangement />

          {/* Hand History */}
          {/* <HandHistory /> */}

          {/* Deal button removed as requested */}

          {/* Show Balls Cards for the host */}
          {/* <BallsCards /> */}
          {/* Player information display */}
          {/* <div className="absolute top-0 left-0 right-0 z-40 bg-black/70 p-2 border-b border-[#E1C760] player-info-section">
            <div className="flex justify-between items-center">
              <div className="flex flex-col items-center">
                <h3 className="text-[#E1C760] text-sm font-semibold md:text-base">
                  {teamNames?.[1] || "Team 1"} <span className="hidden sm:inline">(Us & Partner)</span>
                </h3>
                <div className="flex space-x-2 sm:space-x-4">
                  {players
                    .filter((p) => p.team === 1)
                    .map((player, index) => (
                      <div
                        key={player.id}
                        className="flex flex-col items-center relative"
                      >
                        {player.isDealer && (
                          <div className="absolute -inset-1 border-2 border-[#E1C760] rounded-lg animate-pulse"></div>
                        )}
                        <div className="text-[#E1C760] text-xs font-bold mb-0.5 sm:mb-1">
                          {index === 0 ? "Us" : "Partner"}
                        </div>
                        <div
                          className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden border-2 player-avatar ${
                            player.isDealer
                              ? "border-[#E1C760] ring-2 ring-[#E1C760]/50"
                              : "border-[#E1C760]/70"
                          }`}
                        >
                          <img
                            src={player.avatar}
                            alt={player.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <span className="text-xs text-white mt-0.5 sm:mt-1 truncate max-w-[60px] sm:max-w-none">
                          {player.name}
                        </span>
                        {player.isDealer && (
                          <span className="text-[10px] sm:text-xs font-bold bg-[#E1C760] text-black px-1 sm:px-2 py-0.5 rounded-md shadow-md mt-0.5 sm:mt-1">
                            DEALER
                          </span>
                        )}
                      </div>
                    ))}
                </div>
              </div>
              <div className="flex flex-col items-center">
                <h3 className="text-[#E1C760] text-sm font-semibold md:text-base">
                  {teamNames?.[2] || "Team 2"} <span className="hidden sm:inline">(Opp & Right)</span>
                </h3>
                <div className="flex space-x-2 sm:space-x-4">
                  {players
                    .filter((p) => p.team === 2)
                    .map((player, index) => (
                      <div
                        key={player.id}
                        className="flex flex-col items-center relative"
                      >
                        {player.isDealer && (
                          <div className="absolute -inset-1 border-2 border-[#E1C760] rounded-lg animate-pulse"></div>
                        )}
                        <div className="text-[#E1C760] text-xs font-bold mb-0.5 sm:mb-1">
                          {index === 0 ? "Opp" : "Right"}
                        </div>
                        <div
                          className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden border-2 player-avatar ${
                            player.isDealer
                              ? "border-[#E1C760] ring-2 ring-[#E1C760]/50"
                              : "border-[#E1C760]/70"
                          }`}
                        >
                          <img
                            src={player.avatar}
                            alt={player.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <span className="text-xs text-white mt-0.5 sm:mt-1 truncate max-w-[60px] sm:max-w-none">
                          {player.name}
                        </span>
                        {player.isDealer && (
                          <span className="text-[10px] sm:text-xs font-bold bg-[#E1C760] text-black px-1 sm:px-2 py-0.5 rounded-md shadow-md mt-0.5 sm:mt-1">
                            DEALER
                          </span>
                        )}
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>

 */}

          {/* Main content with adjusted padding */}
          <div className="h-full w-full grid grid-rows-2 pt-28 overflow-hidden main-content">
          <div className="absolute left-0 right-0 z-40 top-0">
            <PlayerTeamBallCards />
            <OppositeTeamBallCards />
          </div>

            {/* Top row for played cards - PlayedCardsArrangement is now a fixed overlay */}
            <div className="w-full flex items-center justify-center">
              <div
                id="played-cards-area"
                className="w-full h-full flex items-center justify-center played-cards-area"
              />
            </div>
            {/* second half content */}
            <div className="w-full relative pt-20 grid grid-rows-3 grid-cols-4">
              <div className="col-span-4 row-start-1 w-full">
                <div className="min-w-max">
                  <PlayerHand cards={hand} />
                </div>
                {/* <GameDrawer /> */}
              </div>
              <div className="col-span-4 row-start-2 w-full flex justify-center">
                {/* Trump Position Selector removed as requested */}
              </div>

              {/* Game controls are now positioned above the bottom navigation */}


            </div>
          </div>
          <CollapsibleGameControls onShowRules={() => setShowRules(true)} />

          {/* <BottomNavigation /> */}

          {/* Hand and Jordhi History */}
          {/* <HandHistory />
          <JordhiHistory /> */}
          <IncorrectJordhiModal />
          <UnderChoppedModal />
          <UnderChoppedResultDisplay
            isOpen={underChoppedResultOpen}
            onClose={() => setUnderChoppedResultOpen(false)}
            result={underChoppedResult}
          />

          {/* Never Follow Suit Modal */}
          <NeverFollowSuitModal
            isOpen={useGameStore.getState().neverFollowSuitModalOpen}
            onClose={() => {
              // Make sure we have the current player ID set before closing
              const currentState = useGameStore.getState();
              if (!currentState.currentPlayerId) {
                const socketId = socketService.getSocketId();
                if (socketId) {
                  console.log("Setting currentPlayerId before closing modal:", socketId);
                  useGameStore.getState().updateGameState({
                    currentPlayerId: socketId,
                    neverFollowSuitModalOpen: false
                  });
                  return;
                }
              }
              useGameStore.getState().updateGameState({ neverFollowSuitModalOpen: false });
            }}
          />

          {/* Four Ball Result Display */}
          <FourBallResultDisplay
            isOpen={showFourBallResult}
            onClose={() => setShowFourBallResult(false)}
            result={fourBallResultData}
          />

          {/* Never Follow Suit Result Display */}
          <NeverFollowSuitResultDisplay
            isOpen={showNeverFollowSuitResult}
            onClose={() => setShowNeverFollowSuitResult(false)}
            result={neverFollowSuitResultData}
          />

          {/* Thunee Result Display */}
          <ThuneeResultDisplay
            isOpen={showThuneeResult}
            onClose={() => setShowThuneeResult(false)}
            result={thuneeResultData}
          />

          {/* Game End Display */}
          <GameEndDisplay
            isVisible={showGameEndDisplay}
            onClose={() => setShowGameEndDisplay(false)}
          />

          {/* Final card dealing animation removed as requested */}
            </>
          )}

          {/* Card Animations - moved outside the gameplay phase so they can be shown at any time */}
          <CardShuffleAnimation
            isVisible={showShuffleAnimation}
            shuffleType={shuffleType}
            onComplete={() => {
              setShowShuffleAnimation(false);
            }}
            duration={5}
          />

          {/* Cut Animation */}
          <CutAnimation
            isVisible={showCutAnimation}
            position={cutPosition}
            onComplete={() => {
              setShowCutAnimation(false);
            }}
          />

          {/* Dealer Card Animation - for the dealer only */}
          <DealerCardAnimation
            isVisible={showDealerAnimation}
            cardCount={dealingCardCount}
            onComplete={() => {
              setShowDealerAnimation(false);
            }}
          />

          {/* Player Card Animation - for non-dealer players */}
          <PlayerCardAnimation
            isVisible={showPlayerAnimation}
            cardCount={dealingCardCount}
            onComplete={() => {
              setShowPlayerAnimation(false);
            }}
          />

          {/* Environment Indicator - shows current environment and connection status */}
          <EnvironmentIndicator />
        </>
      )}
    </div>
  );
}

export default App;
