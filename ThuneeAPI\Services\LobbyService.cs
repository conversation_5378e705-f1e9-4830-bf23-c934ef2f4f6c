using System.Collections.Concurrent;
using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class LobbyService : ILobbyService
    {
        private readonly ConcurrentDictionary<string, Lobby> _lobbies = new();
        private readonly ConcurrentDictionary<string, string> _socketToLobby = new();
        private readonly ConcurrentDictionary<string, GameLobby> _gameLobbies = new();
        private readonly ConcurrentDictionary<string, string> _socketToGameLobby = new();
        private readonly HashSet<string> _matchQueue = new();
        private readonly object _matchQueueLock = new();

        // Events
        public event Func<string, PlayersUpdatedResponse, Task>? PlayersUpdated;
        public event Func<string, TeamNamesUpdatedResponse, Task>? TeamNamesUpdated;
        public event Func<string, TeamReadyUpdatedResponse, Task>? TeamReadyUpdated;
        public event Func<string, GameStartedResponse, Task>? GameStarted;
        public event Func<string, GamePhaseUpdatedResponse, Task>? GamePhaseUpdated;
        public event Func<string, MatchFoundResponse, Task>? MatchFound;
        public event Func<string, MatchStatusUpdateResponse, Task>? MatchStatusUpdated;

        public Task<CreateLobbyResponse> CreateLobbyAsync(string playerName, string? teamName = null, TimeSettings? timeSettings = null)
        {
            var lobbyCode = GenerateLobbyCode();
            var partnerInviteCode = GenerateLobbyCode();
            var opponentInviteCode = GenerateLobbyCode();

            var team1Name = teamName ?? "Team 1";

            var defaultTimeSettings = new TimeSettings
            {
                PlayTimeframeOptions = new List<int> { 3, 4, 5, 6, 60 },
                VotingTimeLimit = 15,
                ThuneeCallingDurations = new ThuneeCallingDurations
                {
                    Trumper = 5,
                    FirstRemaining = 3,
                    LastRemaining = 2
                },
                TrumpDisplayDuration = 10,
                CardDealingSpeed = 300,
                TimerUpdateInterval = 100
            };

            var lobby = new Lobby
            {
                LobbyCode = lobbyCode,
                PartnerInviteCode = partnerInviteCode,
                OpponentInviteCode = opponentInviteCode,
                GameStarted = false,
                Teams = new Dictionary<int, List<Player>>
                {
                    { 1, new List<Player>() },
                    { 2, new List<Player>() }
                },
                TeamNames = new Dictionary<int, string>
                {
                    { 1, team1Name },
                    { 2, "Team 2" }
                },
                TeamReady = new Dictionary<int, bool>
                {
                    { 1, false },
                    { 2, false }
                },
                IsFindingMatch = false,
                ReadyToStart = false,
                TimeSettings = timeSettings ?? defaultTimeSettings,
                BallScores = new Dictionary<string, int>
                {
                    { "team1", 0 },
                    { "team2", 0 }
                },
                BallPoints = new Dictionary<string, int>
                {
                    { "team1", 0 },
                    { "team2", 0 }
                }
            };

            _lobbies[lobbyCode] = lobby;

            // Store redirect entries for invite codes
            _lobbies[partnerInviteCode] = new Lobby { LobbyCode = lobbyCode }; // Simplified redirect
            _lobbies[opponentInviteCode] = new Lobby { LobbyCode = lobbyCode }; // Simplified redirect

            return Task.FromResult(new CreateLobbyResponse
            {
                LobbyCode = lobbyCode,
                PartnerInviteCode = partnerInviteCode,
                OpponentInviteCode = opponentInviteCode
            });
        }

        public Task<JoinLobbyResponse> JoinLobbyAsync(string lobbyCode, string playerName)
        {
            // Implementation for joining lobby
            // This is a simplified version - full implementation would handle invite codes, team assignment, etc.

            if (!_lobbies.TryGetValue(lobbyCode, out var lobby))
            {
                throw new InvalidOperationException("Lobby not found");
            }

            if (lobby.GameStarted)
            {
                throw new InvalidOperationException("Game already in progress");
            }

            if (lobby.Players.Count >= 4)
            {
                throw new InvalidOperationException("Lobby is full");
            }

            // Determine team assignment logic here
            var team = lobby.Teams[1].Count < 2 ? 1 : 2;

            var player = new Player
            {
                Id = Guid.NewGuid().ToString(), // This would be the connection ID in real implementation
                Name = playerName,
                Avatar = GetAvatarUrl(playerName),
                IsHost = lobby.Players.Count == 0,
                Team = team
            };

            lobby.Players.Add(player);
            lobby.Teams[team].Add(player);

            return Task.FromResult(new JoinLobbyResponse
            {
                ActualLobbyCode = lobbyCode,
                IsInviteCode = false
            });
        }

        public async Task<bool> UpdateTeamNameAsync(string connectionId, string? lobbyCode, int teamNumber, string teamName)
        {
            var lobby = GetLobbyByConnectionId(connectionId) ?? GetLobby(lobbyCode ?? "");
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null || !player.IsHost) return false;

            if (teamNumber != 1 && teamNumber != 2) return false;

            lobby.TeamNames[teamNumber] = teamName ?? $"Team {teamNumber}";

            // Trigger event
            if (TeamNamesUpdated != null)
            {
                await TeamNamesUpdated(lobby.LobbyCode, new TeamNamesUpdatedResponse
                {
                    TeamNames = lobby.TeamNames
                });
            }

            return true;
        }

        public async Task<bool> SetTeamReadyAsync(string connectionId, string? lobbyCode, bool ready)
        {
            var lobby = GetLobbyByConnectionId(connectionId) ?? GetLobby(lobbyCode ?? "");
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            player.IsReady = ready;

            // Check if all players in the team are ready
            var teamPlayers = lobby.Teams[player.Team];
            var allTeamPlayersReady = teamPlayers.Count > 0 && teamPlayers.All(p => p.IsReady);

            lobby.TeamReady[player.Team] = allTeamPlayersReady;

            // Trigger event
            if (TeamReadyUpdated != null)
            {
                await TeamReadyUpdated(lobby.LobbyCode, new TeamReadyUpdatedResponse
                {
                    TeamReady = lobby.TeamReady,
                    Players = lobby.Players
                });
            }

            // Check if both teams are ready to start the game
            if (lobby.TeamReady[1] && lobby.TeamReady[2] &&
                lobby.Teams[1].Count == 2 && lobby.Teams[2].Count == 2)
            {
                lobby.GameStarted = true;

                // Trigger game started event
                if (GameStarted != null)
                {
                    await GameStarted(lobby.LobbyCode, new GameStartedResponse
                    {
                        Players = lobby.Players,
                        Teams = lobby.Teams
                    });
                }

                // Trigger game phase updated event
                if (GamePhaseUpdated != null)
                {
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "play-timeframe-voting",
                        Players = lobby.Players
                    });
                }
            }

            return true;
        }

        public Task<bool> SwitchTeamAsync(string connectionId, string? lobbyCode)
        {
            // Implementation for switching teams
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> StartGameAsync(string connectionId, string? lobbyCode)
        {
            // Implementation for starting game
            return Task.FromResult(false); // Placeholder
        }

        public async Task<bool> FindMatchAsync(string connectionId, string? lobbyCode)
        {
            var lobby = GetLobbyByConnectionId(connectionId) ?? GetLobby(lobbyCode ?? "");
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null || !player.IsHost) return false;

            if (lobby.Teams[1].Count != 2) return false;

            lobby.IsFindingMatch = true;
            AddToMatchQueue(lobby.LobbyCode);

            await CheckForMatchesAsync();

            return true;
        }

        public Task<bool> CancelFindMatchAsync(string connectionId, string? lobbyCode)
        {
            var lobby = GetLobbyByConnectionId(connectionId) ?? GetLobby(lobbyCode ?? "");
            if (lobby == null) return Task.FromResult(false);

            lobby.IsFindingMatch = false;
            RemoveFromMatchQueue(lobby.LobbyCode);

            return Task.FromResult(true);
        }

        public Lobby? GetLobby(string lobbyCode)
        {
            _lobbies.TryGetValue(lobbyCode, out var lobby);
            return lobby;
        }

        public Lobby? GetLobbyByConnectionId(string connectionId)
        {
            if (_socketToLobby.TryGetValue(connectionId, out var lobbyCode))
            {
                return GetLobby(lobbyCode);
            }
            return null;
        }

        public string? GetLobbyCodeByConnectionId(string connectionId)
        {
            _socketToLobby.TryGetValue(connectionId, out var lobbyCode);
            return lobbyCode;
        }

        public void SetLobbyForConnection(string connectionId, string lobbyCode)
        {
            _socketToLobby[connectionId] = lobbyCode;
        }

        public void RemoveConnectionFromLobby(string connectionId)
        {
            _socketToLobby.TryRemove(connectionId, out _);
        }

        public void AddToMatchQueue(string lobbyCode)
        {
            lock (_matchQueueLock)
            {
                _matchQueue.Add(lobbyCode);
            }
        }

        public void RemoveFromMatchQueue(string lobbyCode)
        {
            lock (_matchQueueLock)
            {
                _matchQueue.Remove(lobbyCode);
            }
        }

        public Task CheckForMatchesAsync()
        {
            // Implementation for checking matches
            // This would be similar to the Node.js implementation
            return Task.CompletedTask;
        }

        public string GenerateLobbyCode()
        {
            const string characters = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
            var random = new Random();
            var result = new char[6];

            for (int i = 0; i < 6; i++)
            {
                result[i] = characters[random.Next(characters.Length)];
            }

            return new string(result);
        }

        public string GetAvatarUrl(string name)
        {
            return $"https://api.dicebear.com/7.x/avataaars/svg?seed={name}";
        }

        public GameLobby? CreateGameLobby(string lobby1Code, string lobby2Code)
        {
            // Implementation for creating game lobby
            return null; // Placeholder
        }

        public GameLobby? GetGameLobby(string gameLobbyCode)
        {
            _gameLobbies.TryGetValue(gameLobbyCode, out var gameLobby);
            return gameLobby;
        }

        public void SetGameLobbyForConnection(string connectionId, string gameLobbyCode)
        {
            _socketToGameLobby[connectionId] = gameLobbyCode;
        }

        public string? GetGameLobbyCodeByConnectionId(string connectionId)
        {
            _socketToGameLobby.TryGetValue(connectionId, out var gameLobbyCode);
            return gameLobbyCode;
        }
    }
}
