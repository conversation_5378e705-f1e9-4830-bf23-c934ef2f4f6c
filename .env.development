# Development environment variables for Thunee Frontend
# Used when running npm run dev

# API Configuration
VITE_API_BASE_URL=http://localhost:5000
VITE_SIGNALR_GAME_HUB_URL=http://localhost:5000/gameHub
VITE_SIGNALR_VIDEO_HUB_URL=http://localhost:5000/videoHub

# Environment
VITE_APP_ENV=development

# Debug
VITE_DEBUG_MODE=true

# Legacy support (for any remaining Socket.IO references)
VITE_SOCKET_SERVER_URL=http://localhost:5000
